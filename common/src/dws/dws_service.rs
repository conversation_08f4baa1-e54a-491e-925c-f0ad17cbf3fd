//! DWS服务基础实现
//!
//! 对应Scala中的DwsService类

use crate::dto::dwd::die_detail_row::DieDetailRow;
use crate::dto::dwd::die_detail_parquet::DieDetailParquet;
use crate::dto::dwd::sub_test_item_detail::SubTestItemDetail;

/// DWS服务基础类
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.service.DwsService
pub struct DwsService;

impl DwsService {
    /// 构建DWS子Die详情 - 从DieDetailParquet
    /// 对应Scala中的buildDwsSubDieDetail方法，处理parquet格式的数据
    pub fn build_dws_sub_die_detail_from_parquet(die_detail: &DieDetailParquet) -> DwsSubDieDetail {
        DwsSubDieDetail {
            CUSTOMER: die_detail.CUSTOMER.clone().unwrap_or_default(),
            SUB_CUSTOMER: die_detail.SUB_CUSTOMER.clone().unwrap_or_default(),
            UPLOAD_TYPE: die_detail.UPLOAD_TYPE.clone().unwrap_or_default(),
            FILE_ID: die_detail.FILE_ID.unwrap_or(0) as u32,
            FILE_NAME: die_detail.FILE_NAME.clone().unwrap_or_default(),
            FILE_TYPE: die_detail.FILE_TYPE.clone().unwrap_or_default(),
            FACTORY: die_detail.FACTORY.clone().unwrap_or_default(),
            FACTORY_SITE: die_detail.FACTORY_SITE.clone().unwrap_or_default(),
            FAB: die_detail.FAB.clone().unwrap_or_default(),
            FAB_SITE: die_detail.FAB_SITE.clone().unwrap_or_default(),
            LOT_TYPE: die_detail.LOT_TYPE.clone().unwrap_or_default(),
            TEST_AREA: die_detail.TEST_AREA.clone().unwrap_or_default(),
            ECID: die_detail.ECID.clone().unwrap_or_default(),
            IS_STANDARD_ECID: die_detail.IS_STANDARD_ECID.map(|v| v as u8),
            SITE: die_detail.SITE.map(|v| v as u32),
            OFFLINE_RETEST: die_detail.OFFLINE_RETEST.map(|v| v as u8),
            OFFLINE_RETEST_IGNORE_TP: die_detail.OFFLINE_RETEST_IGNORE_TP.map(|v| v as u8),
            ONLINE_RETEST: die_detail.ONLINE_RETEST.map(|v| v as u8),
            INTERRUPT: die_detail.INTERRUPT.map(|v| v as u8),
            INTERRUPT_IGNORE_TP: die_detail.INTERRUPT_IGNORE_TP.map(|v| v as u8),
            DUP_RETEST: die_detail.DUP_RETEST.map(|v| v as u8),
            DUP_RETEST_IGNORE_TP: die_detail.DUP_RETEST_IGNORE_TP.map(|v| v as u8),
            BATCH_NUM: die_detail.BATCH_NUM.map(|v| v as u8),
            BATCH_NUM_IGNORE_TP: die_detail.BATCH_NUM_IGNORE_TP.map(|v| v as u8),
            IS_FIRST_TEST: die_detail.IS_FIRST_TEST.map(|v| v as u8),
            IS_FINAL_TEST: die_detail.IS_FINAL_TEST.map(|v| v as u8),
            IS_FIRST_TEST_IGNORE_TP: die_detail.IS_FIRST_TEST_IGNORE_TP.map(|v| v as u8),
            IS_FINAL_TEST_IGNORE_TP: die_detail.IS_FINAL_TEST_IGNORE_TP.map(|v| v as u8),
            IS_DUP_FIRST_TEST: die_detail.IS_DUP_FIRST_TEST.map(|v| v as u8),
            IS_DUP_FINAL_TEST: die_detail.IS_DUP_FINAL_TEST.map(|v| v as u8),
            IS_DUP_FIRST_TEST_IGNORE_TP: die_detail.IS_DUP_FIRST_TEST_IGNORE_TP.map(|v| v as u8),
            IS_DUP_FINAL_TEST_IGNORE_TP: die_detail.IS_DUP_FINAL_TEST_IGNORE_TP.map(|v| v as u8),
            WAFER_ID: die_detail.WAFER_ID.clone().unwrap_or_default(),
            WAFER_NO: die_detail.WAFER_NO.clone().unwrap_or_default(),
            LOT_ID: die_detail.LOT_ID.clone().unwrap_or_default(),
            SBLOT_ID: die_detail.SBLOT_ID.clone().unwrap_or_default(),
            WAFER_LOT_ID: die_detail.WAFER_LOT_ID.clone().unwrap_or_default(),
            PROBER_HANDLER_ID: die_detail.PROBER_HANDLER_ID.clone().unwrap_or_default(),
            TESTER_TYPE: die_detail.TESTER_TYPE.clone().unwrap_or_default(),
            OPERATOR_NAME: die_detail.OPERATOR_NAME.clone().unwrap_or_default(),
            TEST_STAGE: die_detail.TEST_STAGE.clone().unwrap_or_default(),
            DEVICE_ID: die_detail.DEVICE_ID.clone().unwrap_or_default(),
            TEST_PROGRAM: die_detail.TEST_PROGRAM.clone().unwrap_or_default(),
            TEST_TEMPERATURE: die_detail.TEST_TEMPERATURE.clone().unwrap_or_default(),
            TEST_PROGRAM_VERSION: die_detail.TEST_PROGRAM_VERSION.clone().unwrap_or_default(),
            TESTER_NAME: die_detail.TESTER_NAME.clone().unwrap_or_default(),
            PROBECARD_LOADBOARD_ID: die_detail.PROBECARD_LOADBOARD_ID.clone().unwrap_or_default(),
            FLOW_ID: die_detail.FLOW_ID.clone().unwrap_or_default(),
            FLOW_ID_IGNORE_TP: die_detail.FLOW_ID_IGNORE_TP.clone().unwrap_or_default(),
            DIE_CNT: die_detail.DIE_CNT.map(|v| v as u32),
            X_COORD: die_detail.X_COORD,
            Y_COORD: die_detail.Y_COORD,
            DIE_X: die_detail.DIE_X,
            DIE_Y: die_detail.DIE_Y,
            WF_FLAT: die_detail.WF_FLAT.clone().unwrap_or_default(),
            FABWF_ID: die_detail.FABWF_ID.clone().unwrap_or_default(),
            POS_X: die_detail.POS_X.clone().unwrap_or_default(),
            POS_Y: die_detail.POS_Y.clone().unwrap_or_default(),
            RETICLE_X: die_detail.RETICLE_X,
            RETICLE_Y: die_detail.RETICLE_Y,
            SITE_ID: die_detail.SITE_ID.clone().unwrap_or_default(),
            TEST_TIME: die_detail.TEST_TIME.map(|v| v as u32),
            PART_ID: die_detail.PART_ID.clone().unwrap_or_default(),
            SBIN_NUM: die_detail.SBIN_NUM.map(|v| v as u32),
            SBIN_PF: die_detail.SBIN_PF.clone().unwrap_or_default(),
            SBIN_NAM: die_detail.SBIN_NAM.clone().unwrap_or_default(),
            HBIN_NUM: die_detail.HBIN_NUM.map(|v| v as u32),
            HBIN_PF: die_detail.HBIN_PF.clone().unwrap_or_default(),
            HBIN_NAM: die_detail.HBIN_NAM.clone().unwrap_or_default(),
            HBIN: die_detail.HBIN.clone().unwrap_or_default(),
            SBIN: die_detail.SBIN.clone().unwrap_or_default(),
            START_TIME: die_detail.START_TIME.and_then(|ts| chrono::DateTime::from_timestamp_millis(ts)),
            END_TIME: die_detail.END_TIME.and_then(|ts| chrono::DateTime::from_timestamp_millis(ts)),
            START_HOUR_KEY: die_detail.START_HOUR_KEY.clone().unwrap_or_default(),
            START_DAY_KEY: die_detail.START_DAY_KEY.clone().unwrap_or_default(),
            END_HOUR_KEY: die_detail.END_HOUR_KEY.clone().unwrap_or_default(),
            END_DAY_KEY: die_detail.END_DAY_KEY.clone().unwrap_or_default(),
            C_PART_ID: die_detail.C_PART_ID.map(|v| v as u32),
            RETEST_BIN_NUM: die_detail.RETEST_BIN_NUM.clone().unwrap_or_default(),
            PROCESS: die_detail.PROCESS.clone().unwrap_or_default(),
            CREATE_TIME: die_detail.CREATE_TIME.unwrap_or(0),
            UPLOAD_TIME: die_detail.UPLOAD_TIME.unwrap_or(0),
            TEST_HEAD: die_detail.TEST_HEAD.map(|v| v as u32),
            PROBER_HANDLER_TYP: die_detail.PROBER_HANDLER_TYP.clone().unwrap_or_default(),
            PROBECARD_LOADBOARD_TYP: die_detail.PROBECARD_LOADBOARD_TYP.clone().unwrap_or_default(),
        }
    }

    /// 构建DWS子测试项详情
    /// 对应Scala中的buildDwsSubTestItemDetail方法
    pub fn build_dws_sub_test_item_detail(test_item_detail: &SubTestItemDetail) -> DwsSubTestItemDetail {
        DwsSubTestItemDetail {
            FILE_ID: test_item_detail.FILE_ID,
            ONLINE_RETEST: test_item_detail.ONLINE_RETEST,
            MAX_OFFLINE_RETEST: test_item_detail.MAX_OFFLINE_RETEST,
            MAX_ONLINE_RETEST: test_item_detail.MAX_ONLINE_RETEST,
            IS_DIE_FIRST_TEST: test_item_detail.IS_DIE_FIRST_TEST,
            IS_DIE_FINAL_TEST: test_item_detail.IS_DIE_FINAL_TEST,
            IS_FIRST_TEST: test_item_detail.IS_FIRST_TEST,
            IS_FINAL_TEST: test_item_detail.IS_FINAL_TEST,
            IS_FIRST_TEST_IGNORE_TP: test_item_detail.IS_FIRST_TEST_IGNORE_TP,
            IS_FINAL_TEST_IGNORE_TP: test_item_detail.IS_FINAL_TEST_IGNORE_TP,
            IS_DUP_FIRST_TEST: test_item_detail.IS_DUP_FIRST_TEST,
            IS_DUP_FINAL_TEST: test_item_detail.IS_DUP_FINAL_TEST,
            IS_DUP_FIRST_TEST_IGNORE_TP: test_item_detail.IS_DUP_FIRST_TEST_IGNORE_TP,
            IS_DUP_FINAL_TEST_IGNORE_TP: test_item_detail.IS_DUP_FINAL_TEST_IGNORE_TP,
            TEST_SUITE: test_item_detail.TEST_SUITE.clone(),
            CONDITION_SET: test_item_detail.CONDITION_SET.clone(),
            TEST_NUM: test_item_detail.TEST_NUM,
            TEST_TXT: test_item_detail.TEST_TXT.clone().unwrap_or_default(),
            TEST_ITEM: test_item_detail.TEST_ITEM.clone().unwrap_or_default(),
            IS_DIE_FIRST_TEST_ITEM: test_item_detail.IS_DIE_FIRST_TEST_ITEM,
            TESTITEM_TYPE: test_item_detail.TESTITEM_TYPE.clone().unwrap_or_default(),
            TEST_FLG: test_item_detail.TEST_FLG.clone(),
            PARM_FLG: test_item_detail.PARM_FLG.clone(),
            TEST_STATE: test_item_detail.TEST_STATE.clone(),
            TEST_VALUE: test_item_detail.TEST_VALUE,
            UNITS: test_item_detail.UNITS.clone().unwrap_or_default(),
            TEST_RESULT: test_item_detail.TEST_RESULT,
            ORIGIN_TEST_VALUE: test_item_detail.ORIGIN_TEST_VALUE,
            ORIGIN_UNITS: test_item_detail.ORIGIN_UNITS.clone().unwrap_or_default(),
            TEST_ORDER: test_item_detail.TEST_ORDER,
            ALARM_ID: test_item_detail.ALARM_ID.clone(),
            OPT_FLG: test_item_detail.OPT_FLG.clone(),
            RES_SCAL: test_item_detail.RES_SCAL,
            NUM_TEST: test_item_detail.NUM_TEST,
            LLM_SCAL: test_item_detail.LLM_SCAL,
            HLM_SCAL: test_item_detail.HLM_SCAL,
            LO_LIMIT: test_item_detail.LO_LIMIT,
            HI_LIMIT: test_item_detail.HI_LIMIT,
            ORIGIN_HI_LIMIT: test_item_detail.ORIGIN_HI_LIMIT,
            ORIGIN_LO_LIMIT: test_item_detail.ORIGIN_LO_LIMIT,
            C_RESFMT: test_item_detail.C_RESFMT.clone(),
            C_LLMFMT: test_item_detail.C_LLMFMT.clone(),
            C_HLMFMT: test_item_detail.C_HLMFMT.clone(),
            LO_SPEC: test_item_detail.LO_SPEC,
            HI_SPEC: test_item_detail.HI_SPEC,
            HBIN_NUM: test_item_detail.HBIN_NUM,
            SBIN_NUM: test_item_detail.SBIN_NUM,
            SBIN_PF: test_item_detail.SBIN_PF.clone().unwrap_or_default(),
            SBIN_NAM: test_item_detail.SBIN_NAM.clone().unwrap_or_default(),
            HBIN_PF: test_item_detail.HBIN_PF.clone().unwrap_or_default(),
            HBIN_NAM: test_item_detail.HBIN_NAM.clone().unwrap_or_default(),
            HBIN: test_item_detail.HBIN.clone().unwrap_or_default(),
            SBIN: test_item_detail.SBIN.clone().unwrap_or_default(),
            TEST_HEAD: test_item_detail.TEST_HEAD,
            PART_FLG: test_item_detail.PART_FLG.clone(),
            PART_ID: test_item_detail.PART_ID.clone().unwrap_or_default(),
            C_PART_ID: test_item_detail.C_PART_ID,
            ECID: test_item_detail.ECID.clone().unwrap_or_default(),
            ECID_EXT: test_item_detail.ECID_EXT.clone(),
            ECID_EXTRA: test_item_detail.ECID_EXTRA.clone(),
            IS_STANDARD_ECID: test_item_detail.IS_STANDARD_ECID,
            X_COORD: test_item_detail.X_COORD,
            Y_COORD: test_item_detail.Y_COORD,
            DIE_X: test_item_detail.DIE_X,
            DIE_Y: test_item_detail.DIE_Y,
            TEST_TIME: test_item_detail.TEST_TIME,
            PART_TXT: test_item_detail.PART_TXT.clone(),
            PART_FIX: test_item_detail.PART_FIX.clone(),
            SITE: test_item_detail.SITE,
            TOUCH_DOWN_ID: test_item_detail.TOUCH_DOWN_ID,
            WAFER_LOT_ID: test_item_detail.WAFER_LOT_ID.clone(),
            WAFER_ID: test_item_detail.WAFER_ID.clone().unwrap_or_default(),
            WAFER_NO: test_item_detail.WAFER_NO.clone().unwrap_or_default(),
            RETICLE_T_X: test_item_detail.RETICLE_T_X,
            RETICLE_T_Y: test_item_detail.RETICLE_T_Y,
            RETICLE_X: test_item_detail.RETICLE_X,
            RETICLE_Y: test_item_detail.RETICLE_Y,
            SITE_ID: test_item_detail.SITE_ID.clone(),
            VECT_NAM: test_item_detail.VECT_NAM.clone(),
            TIME_SET: test_item_detail.TIME_SET.clone(),
            NUM_FAIL: test_item_detail.NUM_FAIL,
            FAIL_PIN: test_item_detail.FAIL_PIN.clone(),
            CYCL_CNT: test_item_detail.CYCL_CNT,
            REPT_CNT: test_item_detail.REPT_CNT,
            LONG_ATTRIBUTE_SET: test_item_detail.LONG_ATTRIBUTE_SET.clone(),
            STRING_ATTRIBUTE_SET: test_item_detail.STRING_ATTRIBUTE_SET.clone(),
            FLOAT_ATTRIBUTE_SET: test_item_detail.FLOAT_ATTRIBUTE_SET.clone(),
            UID: test_item_detail.UID.clone(),
            TEXT_DAT: test_item_detail.TEXT_DAT.clone(),
            CREATE_HOUR_KEY: test_item_detail.CREATE_HOUR_KEY.clone(),
            CREATE_DAY_KEY: test_item_detail.CREATE_DAY_KEY.clone(),
            CREATE_TIME: test_item_detail.CREATE_TIME,
            EFUSE_EXTRA: test_item_detail.EFUSE_EXTRA.clone(),
            CHIP_ID: test_item_detail.CHIP_ID.clone(),
            CUSTOMER: String::new(), // 将在fill_file_info中填充
            SUB_CUSTOMER: String::new(),
            UPLOAD_TYPE: String::new(),
            FILE_NAME: String::new(),
            FILE_TYPE: String::new(),
            FACTORY: String::new(),
            FACTORY_SITE: String::new(),
            FAB: String::new(),
            FAB_SITE: String::new(),
            LOT_TYPE: String::new(),
            TEST_AREA: String::new(),
            OFFLINE_RETEST: None,
            INTERRUPT: None,
            DUP_RETEST: None,
            BATCH_NUM: None,
            LOT_ID: String::new(),
            SBLOT_ID: String::new(),
            PROBER_HANDLER_ID: String::new(),
            TESTER_TYPE: String::new(),
            TEST_STAGE: String::new(),
            DEVICE_ID: String::new(),
            TEST_PROGRAM: String::new(),
            TEST_TEMPERATURE: String::new(),
            TEST_PROGRAM_VERSION: String::new(),
            TESTER_NAME: String::new(),
            OPERATOR_NAME: String::new(),
            PROBECARD_LOADBOARD_ID: String::new(),
            START_TIME: None,
            END_TIME: None,
            START_HOUR_KEY: String::new(),
            START_DAY_KEY: String::new(),
            END_HOUR_KEY: String::new(),
            END_DAY_KEY: String::new(),
            FLOW_ID: String::new(),
            RETEST_BIN_NUM: String::new(),
            PROCESS: String::new(),
            UPLOAD_TIME: 0,
        }
    }

    /// 填充文件信息
    /// 对应Scala中的fillFileInfo方法
    pub fn fill_file_info(
        mut test_item: DwsSubTestItemDetail,
        file_info: &DieDetailRow, // 在Scala中是FileMap类型
    ) -> DwsSubTestItemDetail {
        test_item.CUSTOMER = file_info.CUSTOMER.clone();
        test_item.SUB_CUSTOMER = file_info.SUB_CUSTOMER.clone();
        test_item.FACTORY = file_info.FACTORY.clone();
        test_item.TEST_AREA = file_info.TEST_AREA.clone();
        test_item.DEVICE_ID = file_info.DEVICE_ID.clone();
        test_item.LOT_ID = file_info.LOT_ID.clone();
        test_item.SBLOT_ID = file_info.SBLOT_ID.clone();
        test_item.TEST_STAGE = file_info.TEST_STAGE.clone();
        test_item.LOT_TYPE = file_info.LOT_TYPE.clone();
        test_item.TEST_PROGRAM = file_info.TEST_PROGRAM.clone();
        test_item.TEST_TEMPERATURE = file_info.TEST_TEMPERATURE.clone();
        test_item.START_TIME = file_info.START_TIME;
        test_item.END_TIME = file_info.END_TIME;
        test_item.START_HOUR_KEY = file_info.START_HOUR_KEY.clone();
        test_item.START_DAY_KEY = file_info.START_DAY_KEY.clone();
        test_item.END_HOUR_KEY = file_info.END_HOUR_KEY.clone();
        test_item.END_DAY_KEY = file_info.END_DAY_KEY.clone();
        test_item.PROBER_HANDLER_ID = file_info.PROBER_HANDLER_ID.clone();
        test_item.TESTER_TYPE = file_info.TESTER_TYPE.clone();
        test_item.OPERATOR_NAME = file_info.OPERATOR_NAME.clone();
        test_item.PROBECARD_LOADBOARD_ID = file_info.PROBECARD_LOADBOARD_ID.clone();
        test_item.FLOW_ID = file_info.FLOW_ID.clone();
        test_item.PROCESS = file_info.PROCESS.clone();
        test_item.UPLOAD_TIME = file_info.UPLOAD_TIME.timestamp_millis();

        test_item
    }

    /// 将字符串集合去重并用逗号连接
    /// 对应Scala中的DwsCommonUtil.mkStringDistinct方法
    pub fn mk_string_distinct(strings: &[&String]) -> String {
        let mut unique_strings: Vec<String> = strings
            .iter()
            .filter(|s| !s.is_empty())
            .map(|s| s.to_string())
            .collect::<std::collections::HashSet<_>>()
            .into_iter()
            .collect();

        unique_strings.sort();
        unique_strings.join(",")
    }

    /// 判断是否为CP测试区域
    /// 参考Scala中的SUPPORT_CP_TEST_AREA_LIST.contains(TestArea.of(testArea))
    /// 对应Scala中的DwsService.isCpTestArea方法
    pub fn is_cp_test_area(test_area: &str) -> bool {
        use crate::model::constant::test_area::TestArea;

        // 获取CP测试区域列表
        let cp_test_area_list = TestArea::get_cp_list();

        // 将字符串转换为TestArea枚举
        if let Some(test_area_enum) = TestArea::of(test_area) {
            // 检查是否在CP测试区域列表中
            cp_test_area_list.contains(&test_area_enum)
        } else {
            // 如果无法识别的测试区域，默认返回false
            false
        }
    }
}

/// DWS子Die详情
/// 对应Scala中的SubFileDetail类
#[derive(Debug, Clone)]
#[allow(non_snake_case)]
pub struct DwsSubFileDetail {
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: String,
    pub UPLOAD_TYPE: String,
    pub FILE_ID: u32,
    pub FILE_NAME: String,
    pub FILE_TYPE: String,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub FAB: String,
    pub FAB_SITE: String,
    pub LOT_TYPE: String,
    pub TEST_AREA: String,
    pub OFFLINE_RETEST: Option<i32>,
    pub INTERRUPT: Option<i32>,
    pub DUP_RETEST: Option<i32>,
    pub BATCH_NUM: Option<i32>,
    pub LOT_ID: String,
    pub SBLOT_ID: String,
    pub PROBER_HANDLER_ID: String,
    pub TESTER_TYPE: String,
    pub TEST_STAGE: String,
    pub DEVICE_ID: String,
    pub TEST_PROGRAM: String,
    pub TEST_TEMPERATURE: String,
    pub TEST_PROGRAM_VERSION: String,
    pub TESTER_NAME: String,
    pub PROBECARD_LOADBOARD_ID: String,
    pub START_TIME: Option<chrono::DateTime<chrono::Utc>>,
    pub END_TIME: Option<chrono::DateTime<chrono::Utc>>,
    pub START_HOUR_KEY: String,
    pub START_DAY_KEY: String,
    pub END_HOUR_KEY: String,
    pub END_DAY_KEY: String,
    pub FLOW_ID: String,
    pub RETEST_BIN_NUM: String,
    pub PROCESS: String,
    pub UPLOAD_TIME: i64,
}

/// 对应Scala中的SubDieDetail类
#[derive(Debug, Clone)]
#[allow(non_snake_case)]
pub struct DwsSubDieDetail {
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: String,
    pub UPLOAD_TYPE: String,
    pub FILE_ID: u32,
    pub FILE_NAME: String,
    pub FILE_TYPE: String,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub FAB: String,
    pub FAB_SITE: String,
    pub LOT_TYPE: String,
    pub TEST_AREA: String,
    pub ECID: String,
    pub IS_STANDARD_ECID: Option<u8>,
    pub SITE: Option<u32>,
    pub OFFLINE_RETEST: Option<u8>,
    pub OFFLINE_RETEST_IGNORE_TP: Option<u8>,
    pub ONLINE_RETEST: Option<u8>,
    pub INTERRUPT: Option<u8>,
    pub INTERRUPT_IGNORE_TP: Option<u8>,
    pub DUP_RETEST: Option<u8>,
    pub DUP_RETEST_IGNORE_TP: Option<u8>,
    pub BATCH_NUM: Option<u8>,
    pub BATCH_NUM_IGNORE_TP: Option<u8>,
    pub IS_FIRST_TEST: Option<u8>,
    pub IS_FINAL_TEST: Option<u8>,
    pub IS_FIRST_TEST_IGNORE_TP: Option<u8>,
    pub IS_FINAL_TEST_IGNORE_TP: Option<u8>,
    pub IS_DUP_FIRST_TEST: Option<u8>,
    pub IS_DUP_FINAL_TEST: Option<u8>,
    pub IS_DUP_FIRST_TEST_IGNORE_TP: Option<u8>,
    pub IS_DUP_FINAL_TEST_IGNORE_TP: Option<u8>,
    pub WAFER_ID: String,
    pub WAFER_NO: String,
    pub LOT_ID: String,
    pub SBLOT_ID: String,
    pub WAFER_LOT_ID: String,
    pub PROBER_HANDLER_ID: String,
    pub TESTER_TYPE: String,
    pub OPERATOR_NAME: String,
    pub TEST_STAGE: String,
    pub DEVICE_ID: String,
    pub TEST_PROGRAM: String,
    pub TEST_TEMPERATURE: String,
    pub TEST_PROGRAM_VERSION: String,
    pub TESTER_NAME: String,
    pub PROBECARD_LOADBOARD_ID: String,
    pub FLOW_ID: String,
    pub FLOW_ID_IGNORE_TP: String,
    pub DIE_CNT: Option<u32>,
    pub X_COORD: Option<i32>,
    pub Y_COORD: Option<i32>,
    pub DIE_X: Option<i32>,
    pub DIE_Y: Option<i32>,
    pub WF_FLAT: String,
    pub FABWF_ID: String,
    pub POS_X: String,
    pub POS_Y: String,
    pub RETICLE_X: Option<i32>,
    pub RETICLE_Y: Option<i32>,
    pub SITE_ID: String,
    pub TEST_TIME: Option<u32>,
    pub PART_ID: String,
    pub SBIN_NUM: Option<u32>,
    pub SBIN_PF: String,
    pub SBIN_NAM: String,
    pub HBIN_NUM: Option<u32>,
    pub HBIN_PF: String,
    pub HBIN_NAM: String,
    pub HBIN: String,
    pub SBIN: String,
    pub START_TIME: Option<chrono::DateTime<chrono::Utc>>,
    pub END_TIME: Option<chrono::DateTime<chrono::Utc>>,
    pub START_HOUR_KEY: String,
    pub START_DAY_KEY: String,
    pub END_HOUR_KEY: String,
    pub END_DAY_KEY: String,
    pub C_PART_ID: Option<u32>,
    pub RETEST_BIN_NUM: String,
    pub PROCESS: String,
    pub CREATE_TIME: i64,
    pub UPLOAD_TIME: i64,
    pub TEST_HEAD: Option<u32>,
    pub PROBER_HANDLER_TYP: String,
    pub PROBECARD_LOADBOARD_TYP: String,
}

/// DWS子测试项详情
/// 对应Scala中的SubTestItemDetail类
#[derive(Debug, Clone)]
#[allow(non_snake_case)]
pub struct DwsSubTestItemDetail {
    // 原始测试项详情字段
    pub FILE_ID: Option<i64>,
    pub ONLINE_RETEST: Option<i32>,
    pub MAX_OFFLINE_RETEST: Option<i32>,
    pub MAX_ONLINE_RETEST: Option<i32>,
    pub IS_DIE_FIRST_TEST: Option<i32>,
    pub IS_DIE_FINAL_TEST: Option<i32>,
    pub IS_FIRST_TEST: Option<i32>,
    pub IS_FINAL_TEST: Option<i32>,
    pub IS_FIRST_TEST_IGNORE_TP: Option<i32>,
    pub IS_FINAL_TEST_IGNORE_TP: Option<i32>,
    pub IS_DUP_FIRST_TEST: Option<i32>,
    pub IS_DUP_FINAL_TEST: Option<i32>,
    pub IS_DUP_FIRST_TEST_IGNORE_TP: Option<i32>,
    pub IS_DUP_FINAL_TEST_IGNORE_TP: Option<i32>,
    pub TEST_SUITE: Option<String>,
    pub CONDITION_SET: Option<std::collections::HashMap<String, String>>,
    pub TEST_NUM: Option<i64>,
    pub TEST_TXT: String,
    pub TEST_ITEM: String,
    pub IS_DIE_FIRST_TEST_ITEM: Option<i32>,
    pub TESTITEM_TYPE: String,
    pub TEST_FLG: Option<String>,
    pub PARM_FLG: Option<String>,
    pub TEST_STATE: Option<String>,
    pub TEST_VALUE: Option<f64>,
    pub UNITS: String,
    pub TEST_RESULT: Option<i32>,
    pub ORIGIN_TEST_VALUE: Option<f64>,
    pub ORIGIN_UNITS: String,
    pub TEST_ORDER: Option<i64>,
    pub ALARM_ID: Option<String>,
    pub OPT_FLG: Option<String>,
    pub RES_SCAL: Option<i32>,
    pub NUM_TEST: Option<i32>,
    pub LLM_SCAL: Option<i32>,
    pub HLM_SCAL: Option<i32>,
    pub LO_LIMIT: Option<f64>,
    pub HI_LIMIT: Option<f64>,
    pub ORIGIN_HI_LIMIT: Option<f64>,
    pub ORIGIN_LO_LIMIT: Option<f64>,
    pub C_RESFMT: Option<String>,
    pub C_LLMFMT: Option<String>,
    pub C_HLMFMT: Option<String>,
    pub LO_SPEC: Option<f64>,
    pub HI_SPEC: Option<f64>,
    pub HBIN_NUM: Option<i64>,
    pub SBIN_NUM: Option<i64>,
    pub SBIN_PF: String,
    pub SBIN_NAM: String,
    pub HBIN_PF: String,
    pub HBIN_NAM: String,
    pub HBIN: String,
    pub SBIN: String,
    pub TEST_HEAD: Option<i64>,
    pub PART_FLG: Option<String>,
    pub PART_ID: String,
    pub C_PART_ID: Option<i64>,
    pub ECID: String,
    pub ECID_EXT: Option<String>,
    pub ECID_EXTRA: Option<std::collections::HashMap<String, String>>,
    pub IS_STANDARD_ECID: Option<i32>,
    pub X_COORD: Option<i32>,
    pub Y_COORD: Option<i32>,
    pub DIE_X: Option<i32>,
    pub DIE_Y: Option<i32>,
    pub TEST_TIME: Option<i64>,
    pub PART_TXT: Option<String>,
    pub PART_FIX: Option<String>,
    pub SITE: Option<i64>,
    pub TOUCH_DOWN_ID: Option<i32>,
    pub WAFER_LOT_ID: Option<String>,
    pub WAFER_ID: String,
    pub WAFER_NO: String,
    pub RETICLE_T_X: Option<i32>,
    pub RETICLE_T_Y: Option<i32>,
    pub RETICLE_X: Option<i32>,
    pub RETICLE_Y: Option<i32>,
    pub SITE_ID: Option<String>,
    pub VECT_NAM: Option<String>,
    pub TIME_SET: Option<String>,
    pub NUM_FAIL: Option<i64>,
    pub FAIL_PIN: Option<String>,
    pub CYCL_CNT: Option<i64>,
    pub REPT_CNT: Option<i64>,
    pub LONG_ATTRIBUTE_SET: Option<std::collections::HashMap<String, i64>>,
    pub STRING_ATTRIBUTE_SET: Option<std::collections::HashMap<String, String>>,
    pub FLOAT_ATTRIBUTE_SET: Option<std::collections::HashMap<String, f64>>,
    pub UID: Option<String>,
    pub TEXT_DAT: Option<String>,
    pub CREATE_HOUR_KEY: Option<String>,
    pub CREATE_DAY_KEY: Option<String>,
    pub CREATE_TIME: i64,
    pub EFUSE_EXTRA: Option<std::collections::HashMap<String, String>>,
    pub CHIP_ID: Option<String>,

    // 从文件信息中填充的字段
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: String,
    pub UPLOAD_TYPE: String,
    pub FILE_NAME: String,
    pub FILE_TYPE: String,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub FAB: String,
    pub FAB_SITE: String,
    pub LOT_TYPE: String,
    pub TEST_AREA: String,
    pub OFFLINE_RETEST: Option<i32>,
    pub INTERRUPT: Option<i32>,
    pub DUP_RETEST: Option<i32>,
    pub BATCH_NUM: Option<i32>,
    pub LOT_ID: String,
    pub SBLOT_ID: String,
    pub PROBER_HANDLER_ID: String,
    pub TESTER_TYPE: String,
    pub TEST_STAGE: String,
    pub DEVICE_ID: String,
    pub TEST_PROGRAM: String,
    pub TEST_TEMPERATURE: String,
    pub TEST_PROGRAM_VERSION: String,
    pub TESTER_NAME: String,
    pub OPERATOR_NAME: String,
    pub PROBECARD_LOADBOARD_ID: String,
    pub START_TIME: Option<chrono::DateTime<chrono::Utc>>,
    pub END_TIME: Option<chrono::DateTime<chrono::Utc>>,
    pub START_HOUR_KEY: String,
    pub START_DAY_KEY: String,
    pub END_HOUR_KEY: String,
    pub END_DAY_KEY: String,
    pub FLOW_ID: String,
    pub RETEST_BIN_NUM: String,
    pub PROCESS: String,
    pub UPLOAD_TIME: i64,
}

//! SiteTestItemIndexService 实现
//!
//! 对应Scala中的SiteTestItemIndexService类，用于计算站点测试项索引

use crate::dws::dws_service::{DwsService, DwsSubTestItemDetail};
use crate::utils::{date, stats};
use log::info;
use std::collections::HashMap;

// 添加必要的导入
use crate::dws::model::site_test_item::SiteTestItemIndex;
use crate::model::constant::{F, SYSTEM, EMPTY};
use crate::model::constant::test_area::TestArea;

/// 站点测试项索引服务
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.table.distributed.SiteTestItemIndexService
pub struct SiteTestItemIndexService {
    test_area: String,
}

impl SiteTestItemIndexService {
    /// 创建新的SiteTestItemIndexService实例
    pub fn new(test_area: String) -> Self {
        Self { test_area }
    }

    /// 获取支持的CP测试区域列表
    /// 对应Scala中的SUPPORT_CP_TEST_AREA_LIST
    fn get_support_cp_test_area_list() -> Vec<TestArea> {
        TestArea::get_cp_list()
    }

    /// 计算wafer_id_key
    /// 对应Scala逻辑：if (SUPPORT_CP_TEST_AREA_LIST.contains(TestArea.of(item.TEST_AREA))) item.WAFER_ID else EMPTY
    fn calculate_wafer_id_key(test_area: &str, wafer_id: &str) -> String {
        if let Some(area) = TestArea::of(test_area) {
            if Self::get_support_cp_test_area_list().contains(&area) {
                wafer_id.to_string()
            } else {
                EMPTY.to_string()
            }
        } else {
            EMPTY.to_string()
        }
    }

    /// 计算wafer_no_key
    /// 对应Scala逻辑：if (SUPPORT_CP_TEST_AREA_LIST.contains(TestArea.of(item.TEST_AREA))) item.WAFER_NO else EMPTY
    fn calculate_wafer_no_key(test_area: &str, wafer_no: &str) -> String {
        if let Some(area) = TestArea::of(test_area) {
            if Self::get_support_cp_test_area_list().contains(&area) {
                wafer_no.to_string()
            } else {
                EMPTY.to_string()
            }
        } else {
            EMPTY.to_string()
        }
    }

    /// 计算站点测试项索引
    /// 对应Scala中的calculate方法
    pub fn calculate(&self, test_item_detail: &[DwsSubTestItemDetail]) -> Vec<SiteTestItemIndex> {
        info!("Calculating site test item index for {} items", test_item_detail.len());

        if DwsService::is_cp_test_area(&self.test_area) {
            self.cp_calculate(test_item_detail)
        } else {
            self.ft_calculate(test_item_detail)
        }
    }

    /// CP工艺计算站点测试项索引
    /// 对应Scala中的cpCalculate方法
    ///
    /// 实现逻辑：
    /// 1. 按FileDieTestItem(FileDie(FILE_ID, ECID), TEST_ITEM)分组
    /// 2. 对每组按ONLINE_RETEST排序，取最小值(finalFlag=0)和最大值(finalFlag=1)
    /// 3. 按HbinPfTestItem(FILE_ID, FINAL_FLAG, TEST_ITEM, HBIN_PF)分组并计算索引
    fn cp_calculate(&self, test_item_detail: &[DwsSubTestItemDetail]) -> Vec<SiteTestItemIndex> {
        // 第一步：按FileDieTestItem分组，对应Scala中的groupByKey(item => FileDieTestItem(FileDie(item.FILE_ID, item.ECID), item.TEST_ITEM))
        let mut file_die_test_item_groups: HashMap<FileDieTestItem, Vec<&DwsSubTestItemDetail>> = HashMap::new();
        for item in test_item_detail {
            let file_die_test_item = FileDieTestItem::new(item);
            file_die_test_item_groups
                .entry(file_die_test_item)
                .or_insert_with(Vec::new)
                .push(item);
        }

        // 第二步：构建基础站点测试项索引，对应Scala中的flatMapGroups
        let mut base_site_test_item_index = Vec::new();
        for (_, items) in file_die_test_item_groups {
            // 按ONLINE_RETEST排序，对应Scala中的sortBy(_.ONLINE_RETEST.toInt)
            let mut sorted_items = items.clone();
            sorted_items.sort_by(|a, b| {
                a.ONLINE_RETEST.unwrap_or(0).cmp(&b.ONLINE_RETEST.unwrap_or(0))
            });

            // ONLINE_RETEST最小的finalFlag=0
            if let Some(head) = sorted_items.first() {
                let head_detail = self.build_sub_site_test_item_detail(head, 0);
                base_site_test_item_index.push(head_detail);
            }

            // ONLINE_RETEST最大的finalFlag=1
            if let Some(last) = sorted_items.last() {
                let last_detail = self.build_sub_site_test_item_detail(last, 1);
                base_site_test_item_index.push(last_detail);
            }
        }

        // 第三步：按HbinPfTestItem分组并计算索引，对应Scala中的第二个groupByKey和mapGroups
        let mut hbin_pf_test_item_groups: HashMap<HbinPfTestItem, Vec<SubSiteTestItemDetail>> = HashMap::new();
        for item in &base_site_test_item_index {
            let hbin_pf_test_item = HbinPfTestItem::new_from_sub(item);
            hbin_pf_test_item_groups
                .entry(hbin_pf_test_item)
                .or_insert_with(Vec::new)
                .push(item.clone());
        }

        let mut computed_site_test_item_index = Vec::new();
        for (_, items) in hbin_pf_test_item_groups {
            let lazy_items: Vec<&SubSiteTestItemDetail> = items.iter().collect();
            let computed = self.compute_site_test_item_index(&lazy_items);
            computed_site_test_item_index.push(computed);
        }

        computed_site_test_item_index
    }

    /// FT工艺计算站点测试项索引
    /// 对应Scala中的ftCalculate方法
    ///
    /// 实现逻辑：
    /// 1. 将每个测试项构建为finalFlag=1的子项，对应Scala中的map(buildSubSiteTestItemDetail(_, 1))
    /// 2. 按SiteTestItem(HbinPfTestItem(FILE_ID, FINAL_FLAG, TEST_ITEM, HBIN_PF), SITE, TESTITEM_TYPE)分组
    /// 3. 计算索引并聚合wafer相关字段(WAFER_ID, WAFER_NO, WAFER_LOT_ID)
    /// 4. 分离FTR类型(TESTITEM_TYPE == "F")和PTR类型
    /// 5. PTR类型按HbinPfTestItem(FILE_ID, FINAL_FLAG, TEST_ITEM, BIN_PF)重新分组并填充索引
    /// 6. 合并FTR和PTR结果
    fn ft_calculate(&self, test_item_detail: &[DwsSubTestItemDetail]) -> Vec<SiteTestItemIndex> {
        // 第一步：构建基础站点测试项索引，对应Scala中的map(buildSubSiteTestItemDetail(_, 1))
        let mut base_site_test_item_index = Vec::new();
        for item in test_item_detail {
            let detail = self.build_sub_site_test_item_detail(item, 1);
            base_site_test_item_index.push(detail);
        }

        // 第二步：按SiteTestItem分组，对应Scala中的groupByKey(item => SiteTestItem(...))
        let mut site_test_item_groups: HashMap<SiteTestItem, Vec<SubSiteTestItemDetail>> = HashMap::new();
        for item in &base_site_test_item_index {
            let site_test_item = SiteTestItem::new(item);
            site_test_item_groups
                .entry(site_test_item)
                .or_insert_with(Vec::new)
                .push(item.clone());
        }

        // 第三步：计算索引并处理wafer字段聚合，对应Scala中的mapGroups
        let mut computed_site_test_item_index = Vec::new();
        for (_, items) in site_test_item_groups {
            let lazy_items: Vec<&SubSiteTestItemDetail> = items.iter().collect();
            let mut computed = self.compute_site_test_item_index(&lazy_items);

            // 聚合wafer相关字段，对应Scala中的waferIdsLazy, waferNosLazy, waferLotIdsLazy
            let wafer_ids: Vec<&str> = items.iter().map(|item| item.WAFER_ID.as_str()).collect();
            let wafer_nos: Vec<&str> = items.iter().map(|item| item.WAFER_NO.as_str()).collect();
            let wafer_lot_ids: Vec<&str> = items.iter().map(|item| item.WAFER_LOT_ID.as_str()).collect();

            // 对应Scala中的copy(WAFER_ID = ..., WAFER_NO = ..., WAFER_LOT_ID = ...)
            computed.WAFER_ID = self.mk_string_distinct(&wafer_ids);
            computed.WAFER_NO = self.mk_string_distinct(&wafer_nos);
            computed.WAFER_LOT_ID = self.mk_string_distinct(&wafer_lot_ids);

            computed_site_test_item_index.push(computed);
        }

        // 第四步：分离FTR类型和PTR类型，对应Scala中的filter操作
        let mut ftr_site_test_item_index = Vec::new();
        let mut ptr_site_test_item_index_raw = Vec::new();

        for item in computed_site_test_item_index {
            if item.TESTITEM_TYPE == F {
                ftr_site_test_item_index.push(item);
            } else {
                ptr_site_test_item_index_raw.push(item);
            }
        }

        // 第五步：PTR类型重新分组并填充索引
        // 注意：Scala代码中使用的是BIN_PF字段，但Rust模型中只有HBIN_PF字段
        // 对应Scala中的：.groupByKey(item => HbinPfTestItem(item.FILE_ID, item.FINAL_FLAG, item.TEST_ITEM, item.BIN_PF))
        let mut hbin_pf_test_item_groups: HashMap<HbinPfTestItem, Vec<SiteTestItemIndex>> = HashMap::new();
        for item in &ptr_site_test_item_index_raw {
            let hbin_pf_test_item = HbinPfTestItem::new_for_ptr_grouping(item);
            hbin_pf_test_item_groups
                .entry(hbin_pf_test_item)
                .or_insert_with(Vec::new)
                .push(item.clone());
        }

        let mut ptr_site_test_item_index = Vec::new();
        for (_, items) in hbin_pf_test_item_groups {
            let filled_items = self.fill_site_test_item_index(&items);
            ptr_site_test_item_index.extend(filled_items);
        }

        // 第六步：合并结果，对应Scala中的union操作
        ftr_site_test_item_index.extend(ptr_site_test_item_index);
        ftr_site_test_item_index
    }

    /// 构建子站点测试项详情
    /// 对应Scala中的buildSubSiteTestItemDetail方法
    fn build_sub_site_test_item_detail(&self, item: &DwsSubTestItemDetail, final_flag: i32) -> SubSiteTestItemDetail {
        SubSiteTestItemDetail {
            CUSTOMER: item.CUSTOMER.clone(),
            SUB_CUSTOMER: item.SUB_CUSTOMER.clone(),
            UPLOAD_TYPE: item.UPLOAD_TYPE.clone(),
            FILE_ID: item.FILE_ID.unwrap_or(0),
            FILE_NAME: item.FILE_NAME.clone(),
            FILE_TYPE: item.FILE_TYPE.clone(),
            FACTORY: item.FACTORY.clone(),
            FACTORY_SITE: item.FACTORY_SITE.clone(),
            FAB: item.FAB.clone(),
            FAB_SITE: item.FAB_SITE.clone(),
            LOT_TYPE: item.LOT_TYPE.clone(),
            TEST_AREA: item.TEST_AREA.clone(),
            ECID: item.ECID.clone(),
            OFFLINE_RETEST: item.OFFLINE_RETEST,
            ONLINE_RETEST: item.ONLINE_RETEST,
            INTERRUPT: item.INTERRUPT,
            DUP_RETEST: item.DUP_RETEST,
            BATCH_NUM: item.BATCH_NUM,
            IS_FIRST_TEST: item.IS_FIRST_TEST,
            IS_FINAL_TEST: item.IS_FINAL_TEST,
            IS_DUP_FIRST_TEST: item.IS_DUP_FIRST_TEST,
            IS_DUP_FINAL_TEST: item.IS_DUP_FINAL_TEST,
            WAFER_ID: item.WAFER_ID.clone(),
            WAFER_NO: item.WAFER_NO.clone(),
            LOT_ID: item.LOT_ID.clone(),
            SBLOT_ID: item.SBLOT_ID.clone(),
            WAFER_LOT_ID: item.WAFER_LOT_ID.clone().unwrap_or_default(),
            PROBER_HANDLER_ID: item.PROBER_HANDLER_ID.clone(),
            TESTER_TYPE: item.TESTER_TYPE.clone(),
            TEST_STAGE: item.TEST_STAGE.clone(),
            DEVICE_ID: item.DEVICE_ID.clone(),
            TEST_PROGRAM: item.TEST_PROGRAM.clone(),
            TEST_TEMPERATURE: item.TEST_TEMPERATURE.clone(),
            TEST_PROGRAM_VERSION: item.TEST_PROGRAM_VERSION.clone(),
            TESTER_NAME: item.TESTER_NAME.clone(),
            PROBECARD_LOADBOARD_ID: item.PROBECARD_LOADBOARD_ID.clone(),
            SITE: item.SITE.unwrap_or(0),
            X_COORD: item.X_COORD,
            Y_COORD: item.Y_COORD,
            PART_ID: item.PART_ID.clone(),
            SBIN_NUM: item.SBIN_NUM.unwrap_or(0),
            SBIN_PF: item.SBIN_PF.clone(),
            SBIN_NAM: item.SBIN_NAM.clone(),
            HBIN_NUM: item.HBIN_NUM.unwrap_or(0),
            HBIN_PF: item.HBIN_PF.clone(),
            HBIN_NAM: item.HBIN_NAM.clone(),
            TESTITEM_TYPE: item.TESTITEM_TYPE.clone(),
            TEST_NUM: item.TEST_NUM.unwrap_or(0),
            TEST_TXT: item.TEST_TXT.clone(),
            TEST_ITEM: item.TEST_ITEM.clone(),
            LO_SPEC: item.LO_SPEC.unwrap_or(0.0),
            HI_SPEC: item.HI_SPEC.unwrap_or(0.0),
            LO_LIMIT: item.LO_LIMIT.unwrap_or(0.0),
            HI_LIMIT: item.HI_LIMIT.unwrap_or(0.0),
            ORIGIN_HI_LIMIT: item.ORIGIN_HI_LIMIT.unwrap_or(0.0),
            ORIGIN_LO_LIMIT: item.ORIGIN_LO_LIMIT.unwrap_or(0.0),
            TEST_VALUE: item.TEST_VALUE.unwrap_or(0.0),
            TEST_RESULT: item.TEST_RESULT.unwrap_or(0),
            START_TIME: item.START_TIME.map(|t| t.timestamp_millis()).unwrap_or(0),
            END_TIME: item.END_TIME.map(|t| t.timestamp_millis()).unwrap_or(0),
            START_HOUR_KEY: item.START_HOUR_KEY.clone(),
            START_DAY_KEY: item.START_DAY_KEY.clone(),
            END_HOUR_KEY: item.END_HOUR_KEY.clone(),
            END_DAY_KEY: item.END_DAY_KEY.clone(),
            FLOW_ID: item.FLOW_ID.clone(),
            UNITS: item.UNITS.clone(),
            ORIGIN_UNITS: item.ORIGIN_UNITS.clone(),
            FINAL_FLAG: final_flag,
            RETEST_BIN_NUM: item.RETEST_BIN_NUM.clone(),
            PROCESS: item.PROCESS.clone(),
            UPLOAD_TIME: item.UPLOAD_TIME,
        }
    }

    /// 计算站点测试项索引
    /// 对应Scala中的computeSiteTestItemIndex方法
    fn compute_site_test_item_index(&self, items: &[&SubSiteTestItemDetail]) -> SiteTestItemIndex {
        if items.is_empty() {
            panic!("Items list is empty");
        }

        let first_item = items[0];

        // 过滤有效测试结果的项
        let valid_test_values: Vec<f64> = items.iter()
            .filter(|item| item.TEST_RESULT < 2)
            .map(|item| item.TEST_VALUE)
            .collect();

        let cnt = items.len() as i64;

        // 计算失败数量
        let fail_cnt = items.iter()
            .filter(|item| item.TEST_RESULT != 1)
            .count() as i64;

        let fail_rate = stats::divide(fail_cnt as f64, cnt as f64);

        let now = chrono::Utc::now().timestamp_millis();
        let create_hour_key = date::get_day_hour(chrono::Utc::now());
        let create_day_key = date::get_day_hour(chrono::Utc::now());

        let test_item_type = first_item.TESTITEM_TYPE.clone();

        if test_item_type == F {
            // FTR类型测试项
            SiteTestItemIndex {
                CUSTOMER: first_item.CUSTOMER.clone(),
                SUB_CUSTOMER: first_item.SUB_CUSTOMER.clone(),
                UPLOAD_TYPE: first_item.UPLOAD_TYPE.clone(),
                FACTORY: first_item.FACTORY.clone(),
                FACTORY_SITE: first_item.FACTORY_SITE.clone(),
                FAB: first_item.FAB.clone(),
                FAB_SITE: first_item.FAB_SITE.clone(),
                TEST_AREA: first_item.TEST_AREA.clone(),
                TEST_STAGE: first_item.TEST_STAGE.clone(),
                LOT_TYPE: first_item.LOT_TYPE.clone(),
                DEVICE_ID: first_item.DEVICE_ID.clone(),
                LOT_ID: first_item.LOT_ID.clone(),
                SBLOT_ID: first_item.SBLOT_ID.clone(),
                WAFER_LOT_ID: first_item.WAFER_LOT_ID.clone(),
                WAFER_ID: first_item.WAFER_ID.clone(),
                WAFER_NO: first_item.WAFER_NO.clone(),
                TEST_PROGRAM: first_item.TEST_PROGRAM.clone(),
                TEST_TEMPERATURE: first_item.TEST_TEMPERATURE.clone(),
                TEST_PROGRAM_VERSION: first_item.TEST_PROGRAM_VERSION.clone(),
                OFFLINE_RETEST: first_item.OFFLINE_RETEST.map(|v| v as i32),
                INTERRUPT: first_item.INTERRUPT.map(|v| v as i32),
                DUP_RETEST: first_item.DUP_RETEST.map(|v| v as i32),
                BATCH_NUM: first_item.BATCH_NUM.map(|v| v as i32),
                LO_LIMIT: first_item.LO_LIMIT,
                HI_LIMIT: first_item.HI_LIMIT,
                ORIGIN_HI_LIMIT: first_item.ORIGIN_HI_LIMIT,
                ORIGIN_LO_LIMIT: first_item.ORIGIN_LO_LIMIT,
                FILE_ID: first_item.FILE_ID,
                FILE_NAME: first_item.FILE_NAME.clone(),
                FILE_TYPE: first_item.FILE_TYPE.clone(),
                TESTER_NAME: first_item.TESTER_NAME.clone(),
                TESTER_TYPE: first_item.TESTER_TYPE.clone(),
                PROBER_HANDLER_ID: first_item.PROBER_HANDLER_ID.clone(),
                PROBECARD_LOADBOARD_ID: first_item.PROBECARD_LOADBOARD_ID.clone(),
                START_TIME: first_item.START_TIME,
                END_TIME: first_item.END_TIME,
                START_HOUR_KEY: first_item.START_HOUR_KEY.clone(),
                START_DAY_KEY: first_item.START_DAY_KEY.clone(),
                END_HOUR_KEY: first_item.END_HOUR_KEY.clone(),
                END_DAY_KEY: first_item.END_DAY_KEY.clone(),
                FLOW_ID: first_item.FLOW_ID.clone(),
                FINAL_FLAG: first_item.FINAL_FLAG,
                SITE: first_item.SITE,
                HBIN_PF: first_item.HBIN_PF.clone(),
                TESTITEM_TYPE: test_item_type,
                TEST_NUM: first_item.TEST_NUM,
                TEST_TXT: first_item.TEST_TXT.clone(),
                TEST_ITEM: first_item.TEST_ITEM.clone(),
                UNITS: first_item.UNITS.clone(),
                ORIGIN_UNITS: first_item.ORIGIN_UNITS.clone(),
                MEDIAN: 0.0,
                MEAN: 0.0,
                MAX: 0.0,
                MIN: 0.0,
                STANDARD_DEVIATION: 0.0,
                RANGE: 0.0,
                IQR: 0.0,
                Q1: 0.0,
                Q3: 0.0,
                CNT: cnt,
                FAIL_CNT: fail_cnt,
                FAIL_RATE: fail_rate,
                CP: 0.0,
                CPK: 0.0,
                MEDIAN_GAP: 0.0,
                MEAN_GAP: 0.0,
                MAX_GAP: 0.0,
                MIN_GAP: 0.0,
                STANDARD_DEVIATION_GAP: 0.0,
                RANGE_GAP: 0.0,
                IQR_GAP: 0.0,
                Q1_GAP: 0.0,
                Q3_GAP: 0.0,
                CNT_GAP: 0,
                CP_GAP: 0.0,
                CPK_GAP: 0.0,
                CREATE_HOUR_KEY: create_hour_key,
                CREATE_DAY_KEY: create_day_key,
                CREATE_TIME: now,
                CREATE_USER: "SYSTEM".to_string(),
                PROCESS: first_item.PROCESS.clone(),
                UPLOAD_TIME: first_item.UPLOAD_TIME,
            }
        } else {
            // 非FTR类型测试项
            let median = stats::calculate_median(&valid_test_values).unwrap_or(0.0);

            // 使用stats模块的统一方法计算各项统计指标
            let mean = stats::calculate_mean(&valid_test_values).unwrap_or(0.0);
            let max_val = stats::calculate_max(&valid_test_values).unwrap_or(0.0);
            let min_val = stats::calculate_min(&valid_test_values).unwrap_or(0.0);
            let standard_deviation = stats::calculate_std_dev(&valid_test_values).unwrap_or(0.0);
            let range_val = stats::calculate_range(&valid_test_values).unwrap_or(0.0);

            // 计算四分位数
            let q1 = stats::calculate_q1(&valid_test_values).unwrap_or(0.0);
            let q3 = stats::calculate_q3(&valid_test_values).unwrap_or(0.0);
            let iqr = stats::calculate_iqr(&valid_test_values).unwrap_or(0.0);

            // 计算CP和CPK值，使用安全的计算函数
            let std_dev_opt = if standard_deviation > 0.0 { Some(standard_deviation) } else { None };
            let mean_opt = Some(mean);
            let lo_limit_opt = if first_item.LO_LIMIT != 0.0 { Some(first_item.LO_LIMIT) } else { None };
            let hi_limit_opt = if first_item.HI_LIMIT != 0.0 { Some(first_item.HI_LIMIT) } else { None };

            let cp = stats::calculate_cp_safe(std_dev_opt, lo_limit_opt, hi_limit_opt).unwrap_or(0.0);
            let cpk = stats::calculate_cpk_safe(mean_opt, std_dev_opt, lo_limit_opt, hi_limit_opt).unwrap_or(0.0);

            SiteTestItemIndex {
                CUSTOMER: first_item.CUSTOMER.clone(),
                SUB_CUSTOMER: first_item.SUB_CUSTOMER.clone(),
                UPLOAD_TYPE: first_item.UPLOAD_TYPE.clone(),
                FACTORY: first_item.FACTORY.clone(),
                FACTORY_SITE: first_item.FACTORY_SITE.clone(),
                FAB: first_item.FAB.clone(),
                FAB_SITE: first_item.FAB_SITE.clone(),
                TEST_AREA: first_item.TEST_AREA.clone(),
                TEST_STAGE: first_item.TEST_STAGE.clone(),
                LOT_TYPE: first_item.LOT_TYPE.clone(),
                DEVICE_ID: first_item.DEVICE_ID.clone(),
                LOT_ID: first_item.LOT_ID.clone(),
                SBLOT_ID: first_item.SBLOT_ID.clone(),
                WAFER_LOT_ID: first_item.WAFER_LOT_ID.clone(),
                WAFER_ID: first_item.WAFER_ID.clone(),
                WAFER_NO: first_item.WAFER_NO.clone(),
                TEST_PROGRAM: first_item.TEST_PROGRAM.clone(),
                TEST_TEMPERATURE: first_item.TEST_TEMPERATURE.clone(),
                TEST_PROGRAM_VERSION: first_item.TEST_PROGRAM_VERSION.clone(),
                OFFLINE_RETEST: first_item.OFFLINE_RETEST.map(|v| v as i32),
                INTERRUPT: first_item.INTERRUPT.map(|v| v as i32),
                DUP_RETEST: first_item.DUP_RETEST.map(|v| v as i32),
                BATCH_NUM: first_item.BATCH_NUM.map(|v| v as i32),
                LO_LIMIT: first_item.LO_LIMIT,
                HI_LIMIT: first_item.HI_LIMIT,
                ORIGIN_HI_LIMIT: first_item.ORIGIN_HI_LIMIT,
                ORIGIN_LO_LIMIT: first_item.ORIGIN_LO_LIMIT,
                FILE_ID: first_item.FILE_ID,
                FILE_NAME: first_item.FILE_NAME.clone(),
                FILE_TYPE: first_item.FILE_TYPE.clone(),
                TESTER_NAME: first_item.TESTER_NAME.clone(),
                TESTER_TYPE: first_item.TESTER_TYPE.clone(),
                PROBER_HANDLER_ID: first_item.PROBER_HANDLER_ID.clone(),
                PROBECARD_LOADBOARD_ID: first_item.PROBECARD_LOADBOARD_ID.clone(),
                START_TIME: first_item.START_TIME,
                END_TIME: first_item.END_TIME,
                START_HOUR_KEY: first_item.START_HOUR_KEY.clone(),
                START_DAY_KEY: first_item.START_DAY_KEY.clone(),
                END_HOUR_KEY: first_item.END_HOUR_KEY.clone(),
                END_DAY_KEY: first_item.END_DAY_KEY.clone(),
                FLOW_ID: first_item.FLOW_ID.clone(),
                FINAL_FLAG: first_item.FINAL_FLAG,
                SITE: first_item.SITE,
                HBIN_PF: first_item.HBIN_PF.clone(),
                TESTITEM_TYPE: test_item_type,
                TEST_NUM: first_item.TEST_NUM,
                TEST_TXT: first_item.TEST_TXT.clone(),
                TEST_ITEM: first_item.TEST_ITEM.clone(),
                UNITS: first_item.UNITS.clone(),
                ORIGIN_UNITS: first_item.ORIGIN_UNITS.clone(),
                MEDIAN: median,
                MEAN: mean,
                MAX: max_val,
                MIN: min_val,
                STANDARD_DEVIATION: standard_deviation,
                RANGE: range_val,
                IQR: iqr,
                Q1: q1,
                Q3: q3,
                CNT: cnt,
                FAIL_CNT: fail_cnt,
                FAIL_RATE: fail_rate,
                CP: cp,
                CPK: cpk,
                MEDIAN_GAP: 0.0,
                MEAN_GAP: 0.0,
                MAX_GAP: 0.0,
                MIN_GAP: 0.0,
                STANDARD_DEVIATION_GAP: 0.0,
                RANGE_GAP: 0.0,
                IQR_GAP: 0.0,
                Q1_GAP: 0.0,
                Q3_GAP: 0.0,
                CNT_GAP: 0,
                CP_GAP: 0.0,
                CPK_GAP: 0.0,
                CREATE_HOUR_KEY: create_hour_key,
                CREATE_DAY_KEY: create_day_key,
                CREATE_TIME: now,
                CREATE_USER: SYSTEM.to_string(),
                PROCESS: first_item.PROCESS.clone(),
                UPLOAD_TIME: first_item.UPLOAD_TIME,
            }
        }
    }

    /// 填充站点测试项索引
    /// 对应Scala中的fillSiteTestItemIndex方法
    fn fill_site_test_item_index(&self, items: &[SiteTestItemIndex]) -> Vec<SiteTestItemIndex> {
        if items.is_empty() {
            return vec![];
        }

        let first_item = &items[0];

        // 分离final_flag为0和1的项
        let mut first_items = Vec::new();
        let mut final_items = Vec::new();

        for item in items {
            if item.FINAL_FLAG == 0 {
                first_items.push(item);
            } else {
                final_items.push(item);
            }
        }

        let mut result = Vec::new();

        // 处理final_flag为0的项
        if !first_items.is_empty() {
            let mut first_item = first_items[0].clone();

            // 计算差异值
            if !final_items.is_empty() {
                let final_item = &final_items[0];
                first_item.MEDIAN_GAP = final_item.MEDIAN - first_item.MEDIAN;
                first_item.MEAN_GAP = final_item.MEAN - first_item.MEAN;
                first_item.MAX_GAP = final_item.MAX - first_item.MAX;
                first_item.MIN_GAP = final_item.MIN - first_item.MIN;
                first_item.STANDARD_DEVIATION_GAP = final_item.STANDARD_DEVIATION - first_item.STANDARD_DEVIATION;
                first_item.RANGE_GAP = final_item.RANGE - first_item.RANGE;
                first_item.IQR_GAP = final_item.IQR - first_item.IQR;
                first_item.Q1_GAP = final_item.Q1 - first_item.Q1;
                first_item.Q3_GAP = final_item.Q3 - first_item.Q3;
                first_item.CNT_GAP = final_item.CNT - first_item.CNT;
                first_item.CP_GAP = final_item.CP - first_item.CP;
                first_item.CPK_GAP = final_item.CPK - first_item.CPK;
            }

            result.push(first_item);
        }

        // 处理final_flag为1的项
        result.extend(final_items.iter().map(|x| (*x).clone()));

        result
    }

    /// 去重并连接字符串
    fn mk_string_distinct(&self, values: &[&str]) -> String {
        let mut unique_values = Vec::new();
        for value in values {
            if !unique_values.contains(&value.to_string()) {
                unique_values.push(value.to_string());
            }
        }
        unique_values.join(",")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;

    #[test]
    fn test_cp_cpk_calculation() {
        let service = SiteTestItemIndexService::new("CP".to_string());

        // 创建测试数据
        let test_items = vec![
            SubSiteTestItemDetail {
                CUSTOMER: "TEST_CUSTOMER".to_string(),
                SUB_CUSTOMER: "TEST_SUB_CUSTOMER".to_string(),
                UPLOAD_TYPE: "AUTO".to_string(),
                FILE_ID: 1,
                FILE_NAME: "test.stdf".to_string(),
                FILE_TYPE: "STDF".to_string(),
                FACTORY: "TEST_FACTORY".to_string(),
                FACTORY_SITE: "TEST_SITE".to_string(),
                FAB: "TEST_FAB".to_string(),
                FAB_SITE: "TEST_FAB_SITE".to_string(),
                LOT_TYPE: "PROD".to_string(),
                TEST_AREA: "CP".to_string(),
                ECID: "TEST_ECID".to_string(),
                OFFLINE_RETEST: Some(0),
                ONLINE_RETEST: Some(0),
                INTERRUPT: Some(0),
                DUP_RETEST: Some(0),
                BATCH_NUM: Some(1),
                IS_FIRST_TEST: Some(1),
                IS_FINAL_TEST: Some(1),
                IS_DUP_FIRST_TEST: Some(0),
                IS_DUP_FINAL_TEST: Some(0),
                WAFER_ID: "TEST_WAFER".to_string(),
                WAFER_NO: "1".to_string(),
                LOT_ID: "TEST_LOT".to_string(),
                SBLOT_ID: "TEST_SBLOT".to_string(),
                WAFER_LOT_ID: "TEST_WAFER_LOT".to_string(),
                PROBER_HANDLER_ID: "HANDLER1".to_string(),
                TESTER_TYPE: "TYPE1".to_string(),
                TEST_STAGE: "TEST_STAGE".to_string(),
                DEVICE_ID: "TEST_DEVICE".to_string(),
                TEST_PROGRAM: "TEST_PROG".to_string(),
                TEST_TEMPERATURE: "25C".to_string(),
                TEST_PROGRAM_VERSION: "1.0".to_string(),
                TESTER_NAME: "TESTER1".to_string(),
                PROBECARD_LOADBOARD_ID: "LOADBOARD1".to_string(),
                SITE: 1,
                X_COORD: Some(10),
                Y_COORD: Some(20),
                PART_ID: "PART_001".to_string(),
                SBIN_NUM: 1,
                SBIN_PF: "P".to_string(),
                SBIN_NAM: "PASS".to_string(),
                HBIN_NUM: 1,
                HBIN_PF: "P".to_string(),
                HBIN_NAM: "PASS".to_string(),
                TESTITEM_TYPE: "P".to_string(), // 非F类型
                TEST_NUM: 1,
                TEST_TXT: "TEST_TXT".to_string(),
                TEST_ITEM: "TEST_ITEM".to_string(),
                LO_SPEC: 0.5,
                HI_SPEC: 11.0,
                LO_LIMIT: 1.0,  // 设置规格限
                HI_LIMIT: 10.0, // 设置规格限
                ORIGIN_HI_LIMIT: 10.0,
                ORIGIN_LO_LIMIT: 1.0,
                TEST_VALUE: 5.5, // 测试值在规格限内
                TEST_RESULT: 1,  // 通过
                START_TIME: Utc::now().timestamp_millis(),
                END_TIME: Utc::now().timestamp_millis(),
                START_HOUR_KEY: "2024010100".to_string(),
                START_DAY_KEY: "20240101".to_string(),
                END_HOUR_KEY: "2024010100".to_string(),
                END_DAY_KEY: "20240101".to_string(),
                FLOW_ID: "FLOW1".to_string(),
                UNITS: "V".to_string(),
                ORIGIN_UNITS: "V".to_string(),
                FINAL_FLAG: 1,
                RETEST_BIN_NUM: "".to_string(),
                PROCESS: "TEST_PROCESS".to_string(),
                UPLOAD_TIME: Utc::now().timestamp_millis(),
            },
            SubSiteTestItemDetail {
                CUSTOMER: "TEST_CUSTOMER".to_string(),
                SUB_CUSTOMER: "TEST_SUB_CUSTOMER".to_string(),
                UPLOAD_TYPE: "AUTO".to_string(),
                FILE_ID: 1,
                FILE_NAME: "test.stdf".to_string(),
                FILE_TYPE: "STDF".to_string(),
                FACTORY: "TEST_FACTORY".to_string(),
                FACTORY_SITE: "TEST_SITE".to_string(),
                FAB: "TEST_FAB".to_string(),
                FAB_SITE: "TEST_FAB_SITE".to_string(),
                LOT_TYPE: "PROD".to_string(),
                TEST_AREA: "CP".to_string(),
                ECID: "TEST_ECID".to_string(),
                OFFLINE_RETEST: Some(0),
                ONLINE_RETEST: Some(0),
                INTERRUPT: Some(0),
                DUP_RETEST: Some(0),
                BATCH_NUM: Some(1),
                IS_FIRST_TEST: Some(1),
                IS_FINAL_TEST: Some(1),
                IS_DUP_FIRST_TEST: Some(0),
                IS_DUP_FINAL_TEST: Some(0),
                WAFER_ID: "TEST_WAFER".to_string(),
                WAFER_NO: "1".to_string(),
                LOT_ID: "TEST_LOT".to_string(),
                SBLOT_ID: "TEST_SBLOT".to_string(),
                WAFER_LOT_ID: "TEST_WAFER_LOT".to_string(),
                PROBER_HANDLER_ID: "HANDLER1".to_string(),
                TESTER_TYPE: "TYPE1".to_string(),
                TEST_STAGE: "TEST_STAGE".to_string(),
                DEVICE_ID: "TEST_DEVICE".to_string(),
                TEST_PROGRAM: "TEST_PROG".to_string(),
                TEST_TEMPERATURE: "25C".to_string(),
                TEST_PROGRAM_VERSION: "1.0".to_string(),
                TESTER_NAME: "TESTER1".to_string(),
                PROBECARD_LOADBOARD_ID: "LOADBOARD1".to_string(),
                SITE: 1,
                X_COORD: Some(10),
                Y_COORD: Some(20),
                PART_ID: "PART_001".to_string(),
                SBIN_NUM: 1,
                SBIN_PF: "P".to_string(),
                SBIN_NAM: "PASS".to_string(),
                HBIN_NUM: 1,
                HBIN_PF: "P".to_string(),
                HBIN_NAM: "PASS".to_string(),
                TESTITEM_TYPE: "P".to_string(),
                TEST_NUM: 1,
                TEST_TXT: "TEST_TXT".to_string(),
                TEST_ITEM: "TEST_ITEM".to_string(),
                LO_SPEC: 0.5,
                HI_SPEC: 11.0,
                LO_LIMIT: 1.0,
                HI_LIMIT: 10.0,
                ORIGIN_HI_LIMIT: 10.0,
                ORIGIN_LO_LIMIT: 1.0,
                TEST_VALUE: 4.5, // 另一个测试值
                TEST_RESULT: 1,
                START_TIME: Utc::now().timestamp_millis(),
                END_TIME: Utc::now().timestamp_millis(),
                START_HOUR_KEY: "2024010100".to_string(),
                START_DAY_KEY: "20240101".to_string(),
                END_HOUR_KEY: "2024010100".to_string(),
                END_DAY_KEY: "20240101".to_string(),
                FLOW_ID: "FLOW1".to_string(),
                UNITS: "V".to_string(),
                ORIGIN_UNITS: "V".to_string(),
                FINAL_FLAG: 1,
                RETEST_BIN_NUM: "".to_string(),
                PROCESS: "TEST_PROCESS".to_string(),
                UPLOAD_TIME: Utc::now().timestamp_millis(),
            },
        ];

        let test_items_refs: Vec<&SubSiteTestItemDetail> = test_items.iter().collect();
        let result = service.compute_site_test_item_index(&test_items_refs);

        // 验证CP和CPK不为0（因为我们设置了有效的规格限）
        assert!(result.CP > 0.0, "CP should be greater than 0");
        assert!(result.CPK > 0.0, "CPK should be greater than 0");

        // 验证其他统计值
        assert_eq!(result.CNT, 2);
        assert_eq!(result.FAIL_CNT, 0); // 两个测试都通过
        assert_eq!(result.FAIL_RATE, 0.0);
        assert!(result.MEAN > 0.0);
        assert!(result.STANDARD_DEVIATION >= 0.0);

        println!("CP: {}, CPK: {}", result.CP, result.CPK);
        println!("Mean: {}, Std Dev: {}", result.MEAN, result.STANDARD_DEVIATION);
    }

    #[test]
    fn test_ftr_type_no_cp_cpk() {
        let service = SiteTestItemIndexService::new("FT".to_string());

        // 创建F类型测试数据
        let test_items = vec![
            SubSiteTestItemDetail {
                CUSTOMER: "TEST_CUSTOMER".to_string(),
                SUB_CUSTOMER: "TEST_SUB_CUSTOMER".to_string(),
                UPLOAD_TYPE: "AUTO".to_string(),
                FILE_ID: 1,
                FILE_NAME: "test.stdf".to_string(),
                FILE_TYPE: "STDF".to_string(),
                FACTORY: "TEST_FACTORY".to_string(),
                FACTORY_SITE: "TEST_SITE".to_string(),
                FAB: "TEST_FAB".to_string(),
                FAB_SITE: "TEST_FAB_SITE".to_string(),
                LOT_TYPE: "PROD".to_string(),
                TEST_AREA: "FT".to_string(),
                ECID: "TEST_ECID".to_string(),
                OFFLINE_RETEST: Some(0),
                ONLINE_RETEST: Some(0),
                INTERRUPT: Some(0),
                DUP_RETEST: Some(0),
                BATCH_NUM: Some(1),
                IS_FIRST_TEST: Some(1),
                IS_FINAL_TEST: Some(1),
                IS_DUP_FIRST_TEST: Some(0),
                IS_DUP_FINAL_TEST: Some(0),
                WAFER_ID: "TEST_WAFER".to_string(),
                WAFER_NO: "1".to_string(),
                LOT_ID: "TEST_LOT".to_string(),
                SBLOT_ID: "TEST_SBLOT".to_string(),
                WAFER_LOT_ID: "TEST_WAFER_LOT".to_string(),
                PROBER_HANDLER_ID: "HANDLER1".to_string(),
                TESTER_TYPE: "TYPE1".to_string(),
                TEST_STAGE: "TEST_STAGE".to_string(),
                DEVICE_ID: "TEST_DEVICE".to_string(),
                TEST_PROGRAM: "TEST_PROG".to_string(),
                TEST_TEMPERATURE: "25C".to_string(),
                TEST_PROGRAM_VERSION: "1.0".to_string(),
                TESTER_NAME: "TESTER1".to_string(),
                PROBECARD_LOADBOARD_ID: "LOADBOARD1".to_string(),
                SITE: 1,
                X_COORD: Some(10),
                Y_COORD: Some(20),
                PART_ID: "PART_001".to_string(),
                SBIN_NUM: 1,
                SBIN_PF: "P".to_string(),
                SBIN_NAM: "PASS".to_string(),
                HBIN_NUM: 1,
                HBIN_PF: "P".to_string(),
                HBIN_NAM: "PASS".to_string(),
                TESTITEM_TYPE: "F".to_string(), // F类型
                TEST_NUM: 1,
                TEST_TXT: "TEST_TXT".to_string(),
                TEST_ITEM: "TEST_ITEM".to_string(),
                LO_SPEC: 0.0,
                HI_SPEC: 0.0,
                LO_LIMIT: 1.0,
                HI_LIMIT: 10.0,
                ORIGIN_HI_LIMIT: 10.0,
                ORIGIN_LO_LIMIT: 1.0,
                TEST_VALUE: 0.0, // F类型通常没有测试值
                TEST_RESULT: 2,  // F类型结果
                START_TIME: Utc::now().timestamp_millis(),
                END_TIME: Utc::now().timestamp_millis(),
                START_HOUR_KEY: "2024010100".to_string(),
                START_DAY_KEY: "20240101".to_string(),
                END_HOUR_KEY: "2024010100".to_string(),
                END_DAY_KEY: "20240101".to_string(),
                FLOW_ID: "FLOW1".to_string(),
                UNITS: "".to_string(),
                ORIGIN_UNITS: "".to_string(),
                FINAL_FLAG: 1,
                RETEST_BIN_NUM: "".to_string(),
                PROCESS: "TEST_PROCESS".to_string(),
                UPLOAD_TIME: Utc::now().timestamp_millis(),
            },
        ];

        let test_items_refs: Vec<&SubSiteTestItemDetail> = test_items.iter().collect();
        let result = service.compute_site_test_item_index(&test_items_refs);

        // 验证F类型的CP和CPK为0
        assert_eq!(result.CP, 0.0, "F type should have CP = 0");
        assert_eq!(result.CPK, 0.0, "F type should have CPK = 0");
        assert_eq!(result.TESTITEM_TYPE, "F");

        // 验证其他统计值也为0（F类型不计算统计值）
        assert_eq!(result.MEAN, 0.0);
        assert_eq!(result.STANDARD_DEVIATION, 0.0);
        assert_eq!(result.MEDIAN, 0.0);
    }

    #[test]
    fn test_mk_string_distinct() {
        let service = SiteTestItemIndexService::new("CP".to_string());

        let values = vec!["test1", "test2", "test1", "", "test3"];
        let result = service.mk_string_distinct(&values);

        // 验证去重和连接
        assert_eq!(result, "test1,test2,,test3");
    }

    #[test]
    fn test_cp_calculate() {
        let service = SiteTestItemIndexService::new("CP".to_string());

        // 创建模拟的DwsSubTestItemDetail数据
        let test_items = vec![
            create_test_dws_item("WAFER1", "1", "LOT1", "P", 1),
            create_test_dws_item("WAFER1", "1", "LOT1", "P", 1), // 相同的FileDieTestItem
        ];

        let result = service.cp_calculate(&test_items);

        // 验证结果不为空
        assert!(!result.is_empty(), "CP calculate should return results");

        println!("CP calculation completed with {} results", result.len());
    }

    #[test]
    fn test_ft_calculate_wafer_aggregation() {
        let service = SiteTestItemIndexService::new("FT".to_string());

        // 创建模拟的DwsSubTestItemDetail数据，包含不同的wafer信息
        let test_items = vec![
            create_test_dws_item("WAFER1", "1", "LOT1", "P", 1),
            create_test_dws_item("WAFER2", "2", "LOT1", "P", 1),
            create_test_dws_item("WAFER1", "1", "LOT2", "P", 1), // 重复的wafer信息
        ];

        let result = service.ft_calculate(&test_items);

        // 验证结果不为空
        assert!(!result.is_empty(), "FT calculate should return results");

        // 验证wafer字段的聚合（具体验证需要根据实际的分组逻辑）
        println!("FT calculation completed with {} results", result.len());
    }

    // 辅助函数：创建测试用的DwsSubTestItemDetail
    fn create_test_dws_item(wafer_id: &str, wafer_no: &str, wafer_lot_id: &str, hbin_pf: &str, test_result: i32) -> DwsSubTestItemDetail {
        DwsSubTestItemDetail {
            FILE_ID: Some(1),
            WAFER_ID: wafer_id.to_string(),
            WAFER_NO: wafer_no.to_string(),
            WAFER_LOT_ID: Some(wafer_lot_id.to_string()),
            HBIN_PF: hbin_pf.to_string(),
            TEST_RESULT: Some(test_result),
            TEST_VALUE: Some(5.0),
            TESTITEM_TYPE: "P".to_string(),
            TEST_ITEM: "TEST_ITEM".to_string(),
            SITE: Some(1),
            ECID: "ECID1".to_string(),
            ONLINE_RETEST: Some(0), // 添加ONLINE_RETEST字段
            LO_LIMIT: Some(1.0),
            HI_LIMIT: Some(10.0),
            CUSTOMER: "TEST_CUSTOMER".to_string(),
            SUB_CUSTOMER: "TEST_SUB_CUSTOMER".to_string(),
            UPLOAD_TYPE: "AUTO".to_string(),
            FACTORY: "TEST_FACTORY".to_string(),
            FACTORY_SITE: "TEST_SITE".to_string(),
            FAB: "TEST_FAB".to_string(),
            FAB_SITE: "TEST_FAB_SITE".to_string(),
            TEST_AREA: "CP".to_string(), // 修改为CP以便测试CP计算
            TEST_STAGE: "TEST_STAGE".to_string(),
            LOT_TYPE: "PROD".to_string(),
            DEVICE_ID: "TEST_DEVICE".to_string(),
            LOT_ID: "TEST_LOT".to_string(),
            SBLOT_ID: "TEST_SBLOT".to_string(),
            TEST_PROGRAM: "TEST_PROG".to_string(),
            TEST_TEMPERATURE: "25C".to_string(),
            TEST_PROGRAM_VERSION: "1.0".to_string(),
            OFFLINE_RETEST: Some(0),
            INTERRUPT: Some(0),
            DUP_RETEST: Some(0),
            BATCH_NUM: Some(1),
            FILE_NAME: "test.stdf".to_string(),
            FILE_TYPE: "STDF".to_string(),
            TESTER_NAME: "TESTER1".to_string(),
            TESTER_TYPE: "TYPE1".to_string(),
            PROBER_HANDLER_ID: "HANDLER1".to_string(),
            PROBECARD_LOADBOARD_ID: "LOADBOARD1".to_string(),
            START_TIME: Some(Utc::now()),
            END_TIME: Some(Utc::now()),
            START_HOUR_KEY: "2024010100".to_string(),
            START_DAY_KEY: "20240101".to_string(),
            END_HOUR_KEY: "2024010100".to_string(),
            END_DAY_KEY: "20240101".to_string(),
            FLOW_ID: "FLOW1".to_string(),
            TEST_NUM: Some(1),
            TEST_TXT: "TEST_TXT".to_string(),
            UNITS: "V".to_string(),
            ORIGIN_UNITS: "V".to_string(),
            PROCESS: "TEST_PROCESS".to_string(),
            UPLOAD_TIME: Utc::now().timestamp_millis(),
            // 其他字段使用默认值
            ..Default::default()
        }
    }
}


/// 子站点测试项详情
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.model.value.SubSiteTestItemDetail
#[derive(Debug, Clone)]
#[allow(non_snake_case)]
pub struct SubSiteTestItemDetail {
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: String,
    pub UPLOAD_TYPE: String,
    pub FILE_ID: i64,
    pub FILE_NAME: String,
    pub FILE_TYPE: String,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub FAB: String,
    pub FAB_SITE: String,
    pub LOT_TYPE: String,
    pub TEST_AREA: String,
    pub ECID: String,
    pub OFFLINE_RETEST: Option<i32>,
    pub ONLINE_RETEST: Option<i32>,
    pub INTERRUPT: Option<i32>,
    pub DUP_RETEST: Option<i32>,
    pub BATCH_NUM: Option<i32>,
    pub IS_FIRST_TEST: Option<i32>,
    pub IS_FINAL_TEST: Option<i32>,
    pub IS_DUP_FIRST_TEST: Option<i32>,
    pub IS_DUP_FINAL_TEST: Option<i32>,
    pub WAFER_ID: String,
    pub WAFER_NO: String,
    pub LOT_ID: String,
    pub SBLOT_ID: String,
    pub WAFER_LOT_ID: String,
    pub PROBER_HANDLER_ID: String,
    pub TESTER_TYPE: String,
    pub TEST_STAGE: String,
    pub DEVICE_ID: String,
    pub TEST_PROGRAM: String,
    pub TEST_TEMPERATURE: String,
    pub TEST_PROGRAM_VERSION: String,
    pub TESTER_NAME: String,
    pub PROBECARD_LOADBOARD_ID: String,
    pub SITE: i64,
    pub X_COORD: Option<i32>,
    pub Y_COORD: Option<i32>,
    pub PART_ID: String,
    pub SBIN_NUM: i64,
    pub SBIN_PF: String,
    pub SBIN_NAM: String,
    pub HBIN_NUM: i64,
    pub HBIN_PF: String,
    pub HBIN_NAM: String,
    pub TESTITEM_TYPE: String,
    pub TEST_NUM: i64,
    pub TEST_TXT: String,
    pub TEST_ITEM: String,
    pub LO_SPEC: f64,
    pub HI_SPEC: f64,
    pub LO_LIMIT: f64,
    pub HI_LIMIT: f64,
    pub ORIGIN_HI_LIMIT: f64,
    pub ORIGIN_LO_LIMIT: f64,
    pub TEST_VALUE: f64,
    pub TEST_RESULT: i32,
    pub START_TIME: i64,
    pub END_TIME: i64,
    pub START_HOUR_KEY: String,
    pub START_DAY_KEY: String,
    pub END_HOUR_KEY: String,
    pub END_DAY_KEY: String,
    pub FLOW_ID: String,
    pub UNITS: String,
    pub ORIGIN_UNITS: String,
    pub FINAL_FLAG: i32,
    pub RETEST_BIN_NUM: String,
    pub PROCESS: String,
    pub UPLOAD_TIME: i64,
}

/// FileDieTestItem键
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.model.key.FileDieTestItem
#[derive(Debug, Clone, Eq, PartialEq, Hash)]
struct FileDieTestItem {
    file_die: FileDie,
    test_item: String,
}

impl FileDieTestItem {
    fn new(item: &DwsSubTestItemDetail) -> Self {
        Self {
            file_die: FileDie::new(item),
            test_item: item.TEST_ITEM.clone(),
        }
    }
}

/// FileDie键
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.model.key.FileDie
#[derive(Debug, Clone, Eq, PartialEq, Hash)]
struct FileDie {
    file_id: i64,
    ecid: String,
}

impl FileDie {
    fn new(item: &DwsSubTestItemDetail) -> Self {
        Self {
            file_id: item.FILE_ID.unwrap_or(0),
            ecid: item.ECID.clone(),
        }
    }
}

/// SiteTestItem键
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.model.key.SiteTestItem
#[derive(Debug, Clone, Eq, PartialEq, Hash)]
struct SiteTestItem {
    hbin_pf_test_item: HbinPfTestItem,
    site: i64,
    testitem_type: String,
}

impl SiteTestItem {
    fn new(item: &SubSiteTestItemDetail) -> Self {
        Self {
            hbin_pf_test_item: HbinPfTestItem::new_from_sub(item),
            site: item.SITE,
            testitem_type: item.TESTITEM_TYPE.clone(),
        }
    }
}

/// HbinPfTestItem键
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.model.key.HbinPfTestItem
#[derive(Debug, Clone, Eq, PartialEq, Hash)]
struct HbinPfTestItem {
    file_id: i64,
    final_flag: i32,
    test_item: String,
    hbin_pf: String,
}

impl HbinPfTestItem {
    fn new(item: &SiteTestItemIndex) -> Self {
        Self {
            file_id: item.FILE_ID,
            final_flag: item.FINAL_FLAG,
            test_item: item.TEST_ITEM.clone(),
            hbin_pf: item.HBIN_PF.clone(),
        }
    }

    fn new_from_sub(item: &SubSiteTestItemDetail) -> Self {
        Self {
            file_id: item.FILE_ID,
            final_flag: item.FINAL_FLAG,
            test_item: item.TEST_ITEM.clone(),
            hbin_pf: item.HBIN_PF.clone(),
        }
    }

    /// 创建用于PTR分组的键，对应Scala代码中的BIN_PF字段
    /// 注意：如果SiteTestItemIndex中没有BIN_PF字段，则使用HBIN_PF
    fn new_for_ptr_grouping(item: &SiteTestItemIndex) -> Self {
        Self {
            file_id: item.FILE_ID,
            final_flag: item.FINAL_FLAG,
            test_item: item.TEST_ITEM.clone(),
            // 这里应该使用BIN_PF，但如果模型中没有这个字段，则使用HBIN_PF
            hbin_pf: item.HBIN_PF.clone(), // 或者 item.BIN_PF.clone() 如果存在的话
        }
    }
}

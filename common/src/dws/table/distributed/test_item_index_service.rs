//! TestItemIndexService 实现
//!
//! 对应Scala中的TestItemIndexService类，用于计算测试项索引

use crate::dws::dws_service::{DwsService, DwsSubTestItemDetail};
use log::info;
use std::collections::HashMap;

// 添加必要的导入
use crate::dws::model::TestItemIndex;
use crate::model::constant::{EMPTY, F, SYSTEM};
use crate::utils::{date, stats};

/// 测试项索引服务
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.table.distributed.TestItemIndexService
pub struct TestItemIndexService {
    test_area: String,
}

impl TestItemIndexService {
    /// 创建新的TestItemIndexService实例
    pub fn new(test_area: String) -> Self {
        Self { test_area }
    }

    /// 计算测试项索引
    /// 对应Scala中的calculate方法
    pub fn calculate(&self, test_item_detail: &[DwsSubTestItemDetail]) -> Vec<TestItemIndex> {
        info!("Calculating test item index for {} items", test_item_detail.len());

        if DwsService::is_cp_test_area(&self.test_area) {
            self.cp_calculate(test_item_detail)
        } else {
            self.ft_calculate(test_item_detail)
        }
    }

    /// CP工艺计算测试项索引
    /// 对应Scala中的cpCalculate方法
    fn cp_calculate(&self, test_item_detail: &[DwsSubTestItemDetail]) -> Vec<TestItemIndex> {
        // 按FileDieTestItem分组
        let mut file_die_test_item_groups: HashMap<FileDieTestItem, Vec<&DwsSubTestItemDetail>> = HashMap::new();
        for item in test_item_detail {
            let file_die_test_item = FileDieTestItem::new(item);
            file_die_test_item_groups
                .entry(file_die_test_item)
                .or_insert_with(Vec::new)
                .push(item);
        }

        // 构建基础测试项索引
        let mut base_test_item_detail = Vec::new();
        for (_, items) in &file_die_test_item_groups {
            // 按ONLINE_RETEST排序
            let mut sorted_items = items.clone();
            sorted_items.sort_by(|a, b| {
                a.ONLINE_RETEST.unwrap_or(0).cmp(&b.ONLINE_RETEST.unwrap_or(0))
            });

            // ONLINE_RETEST最小的finalFlag=0
            if let Some(head) = sorted_items.first() {
                let head_detail = self.build_sub_site_test_item_detail(head, 0);
                base_test_item_detail.push(head_detail);
            }

            // ONLINE_RETEST最大的finalFlag=1
            if let Some(last) = sorted_items.last() {
                let last_detail = self.build_sub_site_test_item_detail(last, 1);
                base_test_item_detail.push(last_detail);
            }
        }

        // 按HbinPfTestItem分组并计算索引
        let mut hbin_pf_test_item_groups: HashMap<HbinPfTestItem, Vec<SubSiteTestItemDetail>> = HashMap::new();
        for item in &base_test_item_detail {
            let hbin_pf_test_item = HbinPfTestItem::new_from_sub(item);
            hbin_pf_test_item_groups
                .entry(hbin_pf_test_item)
                .or_insert_with(Vec::new)
                .push(item.clone());
        }

        let mut result = Vec::new();
        for (_, items) in hbin_pf_test_item_groups {
            let lazy_items: Vec<&SubSiteTestItemDetail> = items.iter().collect();
            let computed = self.compute_test_item_index(&lazy_items);
            result.push(computed);
        }

        result
    }

    /// FT工艺计算测试项索引
    /// 对应Scala中的ftCalculate方法
    fn ft_calculate(&self, test_item_detail: &[DwsSubTestItemDetail]) -> Vec<TestItemIndex> {
        // 构建基础测试项索引
        let mut base_test_item_detail = Vec::new();
        for item in test_item_detail {
            let detail = self.build_sub_site_test_item_detail(item, 1);
            base_test_item_detail.push(detail);
        }

        // 按HbinPfTestItem分组并计算索引
        let mut hbin_pf_test_item_groups: HashMap<HbinPfTestItem, Vec<SubSiteTestItemDetail>> = HashMap::new();
        for item in &base_test_item_detail {
            let hbin_pf_test_item = HbinPfTestItem::new_from_sub(item);
            hbin_pf_test_item_groups
                .entry(hbin_pf_test_item)
                .or_insert_with(Vec::new)
                .push(item.clone());
        }

        let mut result = Vec::new();
        for (_, items) in hbin_pf_test_item_groups {
            let lazy_items: Vec<&SubSiteTestItemDetail> = items.iter().collect();

            let wafer_ids: Vec<&str> = items.iter().map(|item| item.WAFER_ID.as_str()).collect();
            let wafer_nos: Vec<&str> = items.iter().map(|item| item.WAFER_NO.as_str()).collect();
            let wafer_lot_ids: Vec<&str> = items.iter().map(|item| item.WAFER_LOT_ID.as_str()).collect();

            let mut computed = self.compute_test_item_index(&lazy_items);
            computed.WAFER_ID = self.mk_string_distinct(&wafer_ids);
            computed.WAFER_NO = self.mk_string_distinct(&wafer_nos);
            computed.WAFER_LOT_ID = self.mk_string_distinct(&wafer_lot_ids);

            result.push(computed);
        }

        result
    }

    /// 构建子站点测试项详情
    /// 对应Scala中的buildSubSiteTestItemDetail方法
    fn build_sub_site_test_item_detail(&self, item: &DwsSubTestItemDetail, final_flag: i32) -> SubSiteTestItemDetail {
        SubSiteTestItemDetail {
            CUSTOMER: item.CUSTOMER.clone(),
            SUB_CUSTOMER: item.SUB_CUSTOMER.clone(),
            UPLOAD_TYPE: item.UPLOAD_TYPE.clone(),
            FILE_ID: item.FILE_ID.unwrap_or(0),
            FILE_NAME: item.FILE_NAME.clone(),
            FILE_TYPE: item.FILE_TYPE.clone(),
            FACTORY: item.FACTORY.clone(),
            FACTORY_SITE: item.FACTORY_SITE.clone(),
            FAB: item.FAB.clone(),
            FAB_SITE: item.FAB_SITE.clone(),
            LOT_TYPE: item.LOT_TYPE.clone(),
            TEST_AREA: item.TEST_AREA.clone(),
            ECID: item.ECID.clone(),
            OFFLINE_RETEST: item.OFFLINE_RETEST,
            ONLINE_RETEST: item.ONLINE_RETEST,
            INTERRUPT: item.INTERRUPT,
            DUP_RETEST: item.DUP_RETEST,
            BATCH_NUM: item.BATCH_NUM,
            IS_FIRST_TEST: item.IS_FIRST_TEST,
            IS_FINAL_TEST: item.IS_FINAL_TEST,
            IS_DUP_FIRST_TEST: item.IS_DUP_FIRST_TEST,
            IS_DUP_FINAL_TEST: item.IS_DUP_FINAL_TEST,
            WAFER_ID: item.WAFER_ID.clone(),
            WAFER_NO: item.WAFER_NO.clone(),
            LOT_ID: item.LOT_ID.clone(),
            SBLOT_ID: item.SBLOT_ID.clone(),
            WAFER_LOT_ID: item.WAFER_LOT_ID.clone().unwrap_or_else(|| "".to_string()),
            PROBER_HANDLER_ID: item.PROBER_HANDLER_ID.clone(),
            TESTER_TYPE: item.TESTER_TYPE.clone(),
            TEST_STAGE: item.TEST_STAGE.clone(),
            DEVICE_ID: item.DEVICE_ID.clone(),
            TEST_PROGRAM: item.TEST_PROGRAM.clone(),
            TEST_TEMPERATURE: item.TEST_TEMPERATURE.clone(),
            TEST_PROGRAM_VERSION: item.TEST_PROGRAM_VERSION.clone(),
            TESTER_NAME: item.TESTER_NAME.clone(),
            PROBECARD_LOADBOARD_ID: item.PROBECARD_LOADBOARD_ID.clone(),
            SITE: item.SITE.unwrap_or(0),
            X_COORD: item.X_COORD,
            Y_COORD: item.Y_COORD,
            PART_ID: item.PART_ID.clone(),
            SBIN_NUM: item.SBIN_NUM.unwrap_or(0),
            SBIN_PF: item.SBIN_PF.clone(),
            SBIN_NAM: item.SBIN_NAM.clone(),
            HBIN_NUM: item.HBIN_NUM.unwrap_or(0),
            HBIN_PF: item.HBIN_PF.clone(),
            HBIN_NAM: item.HBIN_NAM.clone(),
            TESTITEM_TYPE: item.TESTITEM_TYPE.clone(),
            TEST_NUM: item.TEST_NUM.unwrap_or(0),
            TEST_TXT: item.TEST_TXT.clone(),
            TEST_ITEM: item.TEST_ITEM.clone(),
            LO_SPEC: item.LO_SPEC.unwrap_or(0.0),
            HI_SPEC: item.HI_SPEC.unwrap_or(0.0),
            LO_LIMIT: item.LO_LIMIT.unwrap_or(0.0),
            HI_LIMIT: item.HI_LIMIT.unwrap_or(0.0),
            ORIGIN_HI_LIMIT: item.ORIGIN_HI_LIMIT.unwrap_or(0.0),
            ORIGIN_LO_LIMIT: item.ORIGIN_LO_LIMIT.unwrap_or(0.0),
            TEST_VALUE: item.TEST_VALUE.unwrap_or(0.0),
            TEST_RESULT: item.TEST_RESULT.unwrap_or(0),
            START_TIME: item.START_TIME.map(|t| t.timestamp_millis()).unwrap_or(0),
            END_TIME: item.END_TIME.map(|t| t.timestamp_millis()).unwrap_or(0),
            START_HOUR_KEY: item.START_HOUR_KEY.clone(),
            START_DAY_KEY: item.START_DAY_KEY.clone(),
            END_HOUR_KEY: item.END_HOUR_KEY.clone(),
            END_DAY_KEY: item.END_DAY_KEY.clone(),
            FLOW_ID: item.FLOW_ID.clone(),
            UNITS: item.UNITS.clone(),
            ORIGIN_UNITS: item.ORIGIN_UNITS.clone(),
            FINAL_FLAG: final_flag,
            RETEST_BIN_NUM: item.RETEST_BIN_NUM.clone(),
            PROCESS: item.PROCESS.clone(),
            UPLOAD_TIME: item.UPLOAD_TIME,
        }
    }

    /// 计算测试项索引
    /// 对应Scala中的computeTestItemIndex方法
    fn compute_test_item_index(&self, items: &[&SubSiteTestItemDetail]) -> TestItemIndex {
        if items.is_empty() {
            panic!("Items list is empty");
        }

        let first_item = items[0];

        // 过滤有效测试结果的项
        let valid_test_values: Vec<f64> = items.iter()
            .filter(|item| item.TEST_RESULT < 2)
            .map(|item| item.TEST_VALUE)
            .collect();

        let cnt = items.len() as i64;

        // 计算失败数量
        let fail_cnt = items.iter()
            .filter(|item| item.TEST_RESULT != 1)
            .count() as i64;

        let fail_rate = stats::divide(fail_cnt as f64, cnt as f64);

        let now = chrono::Utc::now().timestamp_millis();
        let create_hour_key = date::get_day_hour(chrono::Utc::now());
        let create_day_key = date::get_day_hour(chrono::Utc::now());

        let test_item_type = first_item.TESTITEM_TYPE.clone();

        if test_item_type == F {
            // F类型测试项
            TestItemIndex {
                CUSTOMER: first_item.CUSTOMER.clone(),
                SUB_CUSTOMER: first_item.SUB_CUSTOMER.clone(),
                UPLOAD_TYPE: first_item.UPLOAD_TYPE.clone(),
                FACTORY: first_item.FACTORY.clone(),
                FACTORY_SITE: first_item.FACTORY_SITE.clone(),
                FAB: first_item.FAB.clone(),
                FAB_SITE: first_item.FAB_SITE.clone(),
                TEST_AREA: first_item.TEST_AREA.clone(),
                TEST_STAGE: first_item.TEST_STAGE.clone(),
                LOT_TYPE: first_item.LOT_TYPE.clone(),
                DEVICE_ID: first_item.DEVICE_ID.clone(),
                LOT_ID: first_item.LOT_ID.clone(),
                SBLOT_ID: first_item.SBLOT_ID.clone(),
                WAFER_LOT_ID: first_item.WAFER_LOT_ID.clone(),
                WAFER_ID: first_item.WAFER_ID.clone(),
                WAFER_ID_KEY: if DwsService::is_cp_test_area(&first_item.TEST_AREA) {
                    first_item.WAFER_ID.clone()
                } else {
                    EMPTY.to_string()
                },
                WAFER_NO: first_item.WAFER_NO.clone(),
                WAFER_NO_KEY: if DwsService::is_cp_test_area(&first_item.TEST_AREA) {
                    first_item.WAFER_NO.clone()
                } else {
                    EMPTY.to_string()
                },
                TEST_PROGRAM: first_item.TEST_PROGRAM.clone(),
                TEST_TEMPERATURE: first_item.TEST_TEMPERATURE.clone(),
                TEST_PROGRAM_VERSION: first_item.TEST_PROGRAM_VERSION.clone(),
                OFFLINE_RETEST: first_item.OFFLINE_RETEST.map(|v| v as i32),
                INTERRUPT: first_item.INTERRUPT.map(|v| v as i32),
                DUP_RETEST: first_item.DUP_RETEST.map(|v| v as i32),
                BATCH_NUM: first_item.BATCH_NUM.map(|v| v as i32),
                LO_LIMIT: first_item.LO_LIMIT,
                HI_LIMIT: first_item.HI_LIMIT,
                ORIGIN_HI_LIMIT: first_item.ORIGIN_HI_LIMIT,
                ORIGIN_LO_LIMIT: first_item.ORIGIN_LO_LIMIT,
                FILE_ID: first_item.FILE_ID,
                FILE_NAME: first_item.FILE_NAME.clone(),
                FILE_TYPE: first_item.FILE_TYPE.clone(),
                TESTER_NAME: first_item.TESTER_NAME.clone(),
                TESTER_TYPE: first_item.TESTER_TYPE.clone(),
                PROBER_HANDLER_ID: first_item.PROBER_HANDLER_ID.clone(),
                PROBECARD_LOADBOARD_ID: first_item.PROBECARD_LOADBOARD_ID.clone(),
                START_TIME: first_item.START_TIME,
                END_TIME: first_item.END_TIME,
                START_HOUR_KEY: first_item.START_HOUR_KEY.clone(),
                START_DAY_KEY: first_item.START_DAY_KEY.clone(),
                END_HOUR_KEY: first_item.END_HOUR_KEY.clone(),
                END_DAY_KEY: first_item.END_DAY_KEY.clone(),
                FLOW_ID: first_item.FLOW_ID.clone(),
                FINAL_FLAG: first_item.FINAL_FLAG,
                BIN_PF: first_item.HBIN_PF.clone(),
                TESTITEM_TYPE: test_item_type,
                TEST_NUM: first_item.TEST_NUM,
                TEST_TXT: first_item.TEST_TXT.clone(),
                TEST_ITEM: first_item.TEST_ITEM.clone(),
                UNITS: first_item.UNITS.clone(),
                ORIGIN_UNITS: first_item.ORIGIN_UNITS.clone(),
                MEDIAN: 0.0,
                MEAN: 0.0,
                MAX: 0.0,
                MIN: 0.0,
                STANDARD_DEVIATION: 0.0,
                RANGE: 0.0,
                IQR: 0.0,
                Q1: 0.0,
                Q3: 0.0,
                CNT: cnt,
                FAIL_CNT: fail_cnt,
                FAIL_RATE: fail_rate,
                CP: 0.0,
                CPK: 0.0,
                RETEST_BIN_NUM: String::new(), // 简化处理
                CREATE_HOUR_KEY: create_hour_key,
                CREATE_DAY_KEY: create_day_key,
                CREATE_TIME: now,
                CREATE_USER: SYSTEM.to_string(),
                VERSION: now,
                PROCESS: first_item.PROCESS.clone(),
                UPLOAD_TIME: first_item.UPLOAD_TIME,
            }
        } else {
            // 非F类型测试项
            // 使用stats模块的统一方法计算各项统计指标
            let median = stats::calculate_median(&valid_test_values).unwrap_or(0.0);
            let mean = stats::calculate_mean(&valid_test_values).unwrap_or(0.0);
            let max_val = stats::calculate_max(&valid_test_values).unwrap_or(0.0);
            let min_val = stats::calculate_min(&valid_test_values).unwrap_or(0.0);
            let standard_deviation = stats::calculate_std_dev(&valid_test_values).unwrap_or(0.0);
            let range_val = stats::calculate_range(&valid_test_values).unwrap_or(0.0);

            // 计算四分位数
            let q1 = stats::calculate_q1(&valid_test_values).unwrap_or(0.0);
            let q3 = stats::calculate_q3(&valid_test_values).unwrap_or(0.0);
            let iqr = stats::calculate_iqr(&valid_test_values).unwrap_or(0.0);

            // 计算CP和CPK值，使用安全的计算函数
            let std_dev_opt = if standard_deviation > 0.0 { Some(standard_deviation) } else { None };
            let mean_opt = Some(mean);
            let lo_limit_opt = if first_item.LO_LIMIT != 0.0 { Some(first_item.LO_LIMIT) } else { None };
            let hi_limit_opt = if first_item.HI_LIMIT != 0.0 { Some(first_item.HI_LIMIT) } else { None };

            let cp = stats::calculate_cp_safe(std_dev_opt, lo_limit_opt, hi_limit_opt).unwrap_or(0.0);
            let cpk = stats::calculate_cpk_safe(mean_opt, std_dev_opt, lo_limit_opt, hi_limit_opt).unwrap_or(0.0);

            TestItemIndex {
                CUSTOMER: first_item.CUSTOMER.clone(),
                SUB_CUSTOMER: first_item.SUB_CUSTOMER.clone(),
                UPLOAD_TYPE: first_item.UPLOAD_TYPE.clone(),
                FACTORY: first_item.FACTORY.clone(),
                FACTORY_SITE: first_item.FACTORY_SITE.clone(),
                FAB: first_item.FAB.clone(),
                FAB_SITE: first_item.FAB_SITE.clone(),
                TEST_AREA: first_item.TEST_AREA.clone(),
                TEST_STAGE: first_item.TEST_STAGE.clone(),
                LOT_TYPE: first_item.LOT_TYPE.clone(),
                DEVICE_ID: first_item.DEVICE_ID.clone(),
                LOT_ID: first_item.LOT_ID.clone(),
                SBLOT_ID: first_item.SBLOT_ID.clone(),
                WAFER_LOT_ID: first_item.WAFER_LOT_ID.clone(),
                WAFER_ID: first_item.WAFER_ID.clone(),
                WAFER_ID_KEY: if DwsService::is_cp_test_area(&first_item.TEST_AREA) {
                    first_item.WAFER_ID.clone()
                } else {
                    EMPTY.to_string()
                },
                WAFER_NO: first_item.WAFER_NO.clone(),
                WAFER_NO_KEY: if DwsService::is_cp_test_area(&first_item.TEST_AREA) {
                    first_item.WAFER_NO.clone()
                } else {
                    EMPTY.to_string()
                },
                TEST_PROGRAM: first_item.TEST_PROGRAM.clone(),
                TEST_TEMPERATURE: first_item.TEST_TEMPERATURE.clone(),
                TEST_PROGRAM_VERSION: first_item.TEST_PROGRAM_VERSION.clone(),
                OFFLINE_RETEST: first_item.OFFLINE_RETEST.map(|v| v as i32),
                INTERRUPT: first_item.INTERRUPT.map(|v| v as i32),
                DUP_RETEST: first_item.DUP_RETEST.map(|v| v as i32),
                BATCH_NUM: first_item.BATCH_NUM.map(|v| v as i32),
                LO_LIMIT: first_item.LO_LIMIT,
                HI_LIMIT: first_item.HI_LIMIT,
                ORIGIN_HI_LIMIT: first_item.ORIGIN_HI_LIMIT,
                ORIGIN_LO_LIMIT: first_item.ORIGIN_LO_LIMIT,
                FILE_ID: first_item.FILE_ID,
                FILE_NAME: first_item.FILE_NAME.clone(),
                FILE_TYPE: first_item.FILE_TYPE.clone(),
                TESTER_NAME: first_item.TESTER_NAME.clone(),
                TESTER_TYPE: first_item.TESTER_TYPE.clone(),
                PROBER_HANDLER_ID: first_item.PROBER_HANDLER_ID.clone(),
                PROBECARD_LOADBOARD_ID: first_item.PROBECARD_LOADBOARD_ID.clone(),
                START_TIME: first_item.START_TIME,
                END_TIME: first_item.END_TIME,
                START_HOUR_KEY: first_item.START_HOUR_KEY.clone(),
                START_DAY_KEY: first_item.START_DAY_KEY.clone(),
                END_HOUR_KEY: first_item.END_HOUR_KEY.clone(),
                END_DAY_KEY: first_item.END_DAY_KEY.clone(),
                FLOW_ID: first_item.FLOW_ID.clone(),
                FINAL_FLAG: first_item.FINAL_FLAG,
                BIN_PF: first_item.HBIN_PF.clone(),
                TESTITEM_TYPE: test_item_type,
                TEST_NUM: first_item.TEST_NUM,
                TEST_TXT: first_item.TEST_TXT.clone(),
                TEST_ITEM: first_item.TEST_ITEM.clone(),
                UNITS: first_item.UNITS.clone(),
                ORIGIN_UNITS: first_item.ORIGIN_UNITS.clone(),
                MEDIAN: median,
                MEAN: mean,
                MAX: max_val,
                MIN: min_val,
                STANDARD_DEVIATION: standard_deviation,
                RANGE: range_val,
                IQR: iqr,
                Q1: q1,
                Q3: q3,
                CNT: cnt,
                FAIL_CNT: fail_cnt,
                FAIL_RATE: fail_rate,
                CP: cp,
                CPK: cpk,
                RETEST_BIN_NUM: first_item.RETEST_BIN_NUM.clone(), // 简化处理
                CREATE_HOUR_KEY: create_hour_key,
                CREATE_DAY_KEY: create_day_key,
                CREATE_TIME: now,
                CREATE_USER: SYSTEM.to_string(),
                VERSION: now,
                PROCESS: first_item.PROCESS.clone(),
                UPLOAD_TIME: first_item.UPLOAD_TIME,
            }
        }
    }

    /// 去重并连接字符串
    fn mk_string_distinct(&self, values: &[&str]) -> String {
        let mut unique_values = Vec::new();
        for value in values {
            if !unique_values.contains(&value.to_string()) {
                unique_values.push(value.to_string());
            }
        }
        unique_values.join(",")
    }
}


/// 子站点测试项详情
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.model.value.SubSiteTestItemDetail
#[derive(Debug, Clone)]
#[allow(non_snake_case)]
pub struct SubSiteTestItemDetail {
    pub CUSTOMER: String,
    pub SUB_CUSTOMER: String,
    pub UPLOAD_TYPE: String,
    pub FILE_ID: i64,
    pub FILE_NAME: String,
    pub FILE_TYPE: String,
    pub FACTORY: String,
    pub FACTORY_SITE: String,
    pub FAB: String,
    pub FAB_SITE: String,
    pub LOT_TYPE: String,
    pub TEST_AREA: String,
    pub ECID: String,
    pub OFFLINE_RETEST: Option<i32>,
    pub ONLINE_RETEST: Option<i32>,
    pub INTERRUPT: Option<i32>,
    pub DUP_RETEST: Option<i32>,
    pub BATCH_NUM: Option<i32>,
    pub IS_FIRST_TEST: Option<i32>,
    pub IS_FINAL_TEST: Option<i32>,
    pub IS_DUP_FIRST_TEST: Option<i32>,
    pub IS_DUP_FINAL_TEST: Option<i32>,
    pub WAFER_ID: String,
    pub WAFER_NO: String,
    pub LOT_ID: String,
    pub SBLOT_ID: String,
    pub WAFER_LOT_ID: String,
    pub PROBER_HANDLER_ID: String,
    pub TESTER_TYPE: String,
    pub TEST_STAGE: String,
    pub DEVICE_ID: String,
    pub TEST_PROGRAM: String,
    pub TEST_TEMPERATURE: String,
    pub TEST_PROGRAM_VERSION: String,
    pub TESTER_NAME: String,
    pub PROBECARD_LOADBOARD_ID: String,
    pub SITE: i64,
    pub X_COORD: Option<i32>,
    pub Y_COORD: Option<i32>,
    pub PART_ID: String,
    pub SBIN_NUM: i64,
    pub SBIN_PF: String,
    pub SBIN_NAM: String,
    pub HBIN_NUM: i64,
    pub HBIN_PF: String,
    pub HBIN_NAM: String,
    pub TESTITEM_TYPE: String,
    pub TEST_NUM: i64,
    pub TEST_TXT: String,
    pub TEST_ITEM: String,
    pub LO_SPEC: f64,
    pub HI_SPEC: f64,
    pub LO_LIMIT: f64,
    pub HI_LIMIT: f64,
    pub ORIGIN_HI_LIMIT: f64,
    pub ORIGIN_LO_LIMIT: f64,
    pub TEST_VALUE: f64,
    pub TEST_RESULT: i32,
    pub START_TIME: i64,
    pub END_TIME: i64,
    pub START_HOUR_KEY: String,
    pub START_DAY_KEY: String,
    pub END_HOUR_KEY: String,
    pub END_DAY_KEY: String,
    pub FLOW_ID: String,
    pub UNITS: String,
    pub ORIGIN_UNITS: String,
    pub FINAL_FLAG: i32,
    pub RETEST_BIN_NUM: String,
    pub PROCESS: String,
    pub UPLOAD_TIME: i64,
}

/// FileDieTestItem键
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.model.key.FileDieTestItem
#[derive(Debug, Clone, Eq, PartialEq, Hash)]
struct FileDieTestItem {
    file_die: FileDie,
    test_item: String,
}

impl FileDieTestItem {
    fn new(item: &DwsSubTestItemDetail) -> Self {
        Self {
            file_die: FileDie::new(item),
            test_item: item.TEST_ITEM.clone(),
        }
    }
}

/// FileDie键
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.model.key.FileDie
#[derive(Debug, Clone, Eq, PartialEq, Hash)]
struct FileDie {
    file_id: i64,
    ecid: String,
}

impl FileDie {
    fn new(item: &DwsSubTestItemDetail) -> Self {
        Self {
            file_id: item.FILE_ID.unwrap_or(0),
            ecid: item.ECID.clone(),
        }
    }
}

/// HbinPfTestItem键
/// 对应Scala中的com.guwave.onedata.dataware.dw.common.dws.model.key.HbinPfTestItem
#[derive(Debug, Clone, Eq, PartialEq, Hash)]
struct HbinPfTestItem {
    file_id: i64,
    final_flag: i32,
    test_item: String,
    hbin_pf: String,
}

impl HbinPfTestItem {
    fn new_from_sub(item: &SubSiteTestItemDetail) -> Self {
        Self {
            file_id: item.FILE_ID,
            final_flag: item.FINAL_FLAG,
            test_item: item.TEST_ITEM.clone(),
            hbin_pf: item.HBIN_PF.clone(),
        }
    }
}

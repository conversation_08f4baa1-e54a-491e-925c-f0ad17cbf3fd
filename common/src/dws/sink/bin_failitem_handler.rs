
use crate::ck::ck_sink::<PERSON><PERSON><PERSON><PERSON><PERSON>;

pub struct BinFailitemHandler {
    db_name: String,
    table_name: String,
}

impl BinFailitemHandler {
    pub fn new(db_name: String) -> Self {
        Self {
            db_name,
            table_name: "dim_bin_failitem_local".to_string(),
        }
    }
}

impl SinkHandler for BinFailitemHandler {
    fn db_name(&self) -> &str {
        &self.db_name
    }

    fn table_name(&self) -> &str {
        &self.table_name
    }

    fn partition_expr(&self) -> &str {
        "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}')"
    }
}

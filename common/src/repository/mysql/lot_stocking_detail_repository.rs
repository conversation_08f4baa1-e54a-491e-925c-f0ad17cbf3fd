use mysql_provider::{MySqlConfig, MySqlProvider, MySqlProviderError, MySqlProviderImpl};
use sqlx::FromRow;
use crate::model::constant::{SUPPORT_TEST_AREA_CP, SUPPORT_TEST_AREA_CP_MAP, SUPPORT_TEST_AREA_CP_INKLESS_MAP};

/// Lot data count result structure
/// Corresponds to: LotData case class in Scala
#[derive(Debug, FromRow)]
pub struct LotData {
    #[sqlx(rename = "dieDataCount")]
    pub die_data_count: Option<i64>,
    #[sqlx(rename = "testItemDataCount")]
    pub test_item_data_count: Option<i64>,
}

/// Lot Stocking Detail Repository
/// Corresponds to: LotStockingDetailRepository.scala
pub struct LotStockingDetailRepository {
    mysql_provider: MySqlProviderImpl,
}

impl LotStockingDetailRepository {
    /// Create new repository instance
    /// Corresponds to: LotStockingDetailRepository constructor in Scala
    pub async fn new(mysql_config: MySqlConfig) -> Result<Self, MySqlProviderError> {
        let mysql_provider = MySqlProviderImpl::new(mysql_config).await?;
        Ok(Self { mysql_provider })
    }

    /// Get supported CP test area list
    /// Corresponds to: SUPPORT_CP_TEST_AREA_LIST = TestArea.getCPList in Scala
    pub fn get_support_cp_test_area_list() -> Vec<&'static str> {
        vec![SUPPORT_TEST_AREA_CP, SUPPORT_TEST_AREA_CP_MAP, SUPPORT_TEST_AREA_CP_INKLESS_MAP]
    }

    /// Check if test area is supported CP test area
    fn is_support_cp_test_area(test_area: &str) -> bool {
        Self::get_support_cp_test_area_list().contains(&test_area)
    }

    /// Count lot data from dw_lot_stocking_detail table
    /// Corresponds to: countLotData method in Scala
    pub async fn count_lot_data(
        &self,
        customer: &str,
        factory: &str,
        factory_site: &str,
        test_area: &str,
        device_id: &str,
        lot_id: &str,
        wafer_no: &str,
        test_stage: &str,
        lot_type: &str,
        file_category: &str,
    ) -> Result<LotData, MySqlProviderError> {
        let wafer_condition = if Self::is_support_cp_test_area(test_area) {
            format!("AND wafer_no = '{}'", wafer_no)
        } else {
            String::new()
        };

        let sql = format!(
            r#"
            SELECT
            CAST(COALESCE(SUM(die_data_count), 0) AS SIGNED) AS dieDataCount,
            CAST(COALESCE(SUM(test_item_data_count), 0) AS SIGNED) AS testItemDataCount
            FROM dw_lot_stocking_detail
            WHERE
            customer = '{}'
            AND factory = '{}'
            AND factory_site = '{}'
            AND test_area = '{}'
            AND device_id = '{}'
            AND lot_id = '{}'
            AND test_stage = '{}'
            AND lot_type = '{}'
            AND file_category = '{}'
            {}
            "#,
            customer,
            factory,
            factory_site,
            test_area,
            device_id,
            lot_id,
            test_stage,
            lot_type,
            file_category,
            wafer_condition
        );

        log::info!("查询条件：{}", sql);
        
        let results = self.mysql_provider.query::<LotData>(&sql).await?;
        Ok(results.into_iter().next().unwrap())
    }
}
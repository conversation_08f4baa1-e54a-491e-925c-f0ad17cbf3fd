use std::collections::HashMap;
use chrono::{DateTime, Utc};
use serde_json;
use log::info;

use common::dws::dws_service::{DwsSubDieDetail, DwsSubTestItemDetail};
use common::dto::dws::test_program_test_order::TestProgramTestOrder;
use common::model::constant::{COMMA, EMPTY, SYSTEM};
use common::model::key::lot_key::LotKey;
use common::repository::mysql::test_program_test_plan_repository::TestProgramTestPlanRepository;
use common::repository::ck::test_program_test_order_repository::TestProgramTestOrderRepository;
use mysql_provider::MySqlConfig;
use ck_provider::CkConfig;

use crate::service::test_program_test_order_common_service::TestProgramTestOrderCommonService;

const PF_PASS: &str = "PASS";

#[derive(Debug, Clone)]
pub struct TestProgramTestOrderService {
    pub test_area: String,
    pub common_service: TestProgramTestOrderCommonService,
}

impl TestProgramTestOrderService {
    pub fn new(test_area: String) -> Self {
        Self {
            test_area,
            common_service: TestProgramTestOrderCommonService::new(),
        }
    }

    /// Main calculation method equivalent to calculate() in Scala
    /// Processes die details and test item details to generate test program test orders
    pub async fn calculate(
        &self,
        die_detail: &Vec<DwsSubDieDetail>,
        test_item_detail: &Vec<DwsSubTestItemDetail>,
        mysql_config: MySqlConfig,
        ck_config: CkConfig,
        lot_key: &LotKey,
        upload_type: &str,
        redis_provider: &redis_provider::provider::RedisProvider,
        write_parquet_path: &str,
        dim_db_name: &str,
        index_num_partition: &str,
        dim_result_partition: i32,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Get distinct test programs from die details
        let test_programs: Vec<String> = die_detail
            .iter()
            .filter(|d| !d.TEST_PROGRAM.is_empty())
            .map(|d| d.TEST_PROGRAM.clone())
            .collect::<std::collections::HashSet<_>>()
            .into_iter()
            .collect();

        // Check which test programs can be initialized
        let test_program_test_order_repository = TestProgramTestOrderRepository::new(
            dim_db_name.to_string(),
            ck_config.clone(),
        );
        let cal_test_program_test_order_programs = test_program_test_order_repository
            .can_init_test_program_test_order(lot_key, upload_type, &test_programs)
            .await?;

        // Calculate initial test order
        let program_with_test_item_map = self.cal_init_test_order(
            die_detail,
            test_item_detail,
            &cal_test_program_test_order_programs,
        )?;

        // Define the write function for HDFS and ClickHouse
        let write_hdfs_and_ck = |hdfs_first_insert_flag: bool, datas: &[TestProgramTestOrder]| {
            // Write to ClickHouse
            // Note: This is a placeholder for the actual ClickHouse write implementation
            // In the Scala version, this uses CkProvider.writeToCk with TestProgramTestOrderHandler
            info!("Writing {} records to ClickHouse (hdfs_first_insert_flag: {})", datas.len(), hdfs_first_insert_flag);
            // TODO: Implement actual ClickHouse write logic here
        };

        // Write test program test order data
        self.common_service
            .write_test_program_test_order(
                &program_with_test_item_map,
                dim_db_name,
                mysql_config,
                ck_config,
                redis_provider,
                lot_key,
                upload_type,
                write_hdfs_and_ck,
            )
            .await?;

        Ok(())
    }

    /// Calculate initial test order equivalent to calInitTestOrder in Scala
    fn cal_init_test_order(
        &self,
        die_detail: &Vec<DwsSubDieDetail>,
        test_item_detail: &Vec<DwsSubTestItemDetail>,
        cal_test_program_test_order_programs: &[String],
    ) -> Result<HashMap<String, HashMap<String, TestProgramTestOrder>>, Box<dyn std::error::Error>> {
        // Filter pass bin dies and group by FILE_ID, then find first die for each file
        let mut file_first_dies = Vec::new();
        let mut grouped_by_file: HashMap<u32, Vec<&DwsSubDieDetail>> = HashMap::new();
        
        for die in die_detail {
            if self.common_service.filter_pass_bin(die, cal_test_program_test_order_programs) {
                grouped_by_file.entry(die.FILE_ID).or_insert_with(Vec::new).push(die);
            }
        }

        // Find first die for each file
        for (_, dies) in grouped_by_file {
            if let Some(first_die) = self.common_service.find_file_first_die(&dies) {
                file_first_dies.push(first_die);
            }
        }

        // Find first die for each program
        let program_first_die = self.common_service.find_program_first_die(&file_first_dies);

        // Filter test item details and generate test program test orders
        let mut test_program_test_orders = Vec::new();
        for test_item in test_item_detail {
            let program = &test_item.TEST_PROGRAM;
            let first_die = program_first_die.get(program);
            
            if self.common_service.predict_program_first_die_test(test_item, first_die) {
                let test_order = self.common_service.generate_test_program_test_order(test_item);
                test_program_test_orders.push(test_order);
            }
        }

        // Calculate test order
        let result = self.common_service.cal_test_order(&test_program_test_orders);
        Ok(result)
    }
}
use std::collections::HashMap;
use common::dto::dws::sub_bin_failitem::SubBinFailitem;
use common::dto::dws::bin_failitem::BinFailitem;
use common::dws::model::key::program_testItem_bin::ProgramTestItemBin;
use common::model::key::LotKey;
use mysql_provider::MySqlConfig;
use crate::service::bin_failitem_common_service::BinFailitemCommonService;


#[derive(Debug, Clone)]
pub struct BinFailitemService {
    pub test_area: String,
    pub common_service: BinFailitemCommonService,
}

impl BinFailitemService {
    pub fn new(test_area: String) -> Self {
        Self {
            test_area,
            common_service: BinFailitemCommonService::new(),
        }
    }

    /// Calculate BinFailitem from SubBinFailitem records
    /// Equivalent to calculate function in Scala (without Spark Dataset operations)
    /// Filters records with ORDER_VALID_FLAG == 1 and non-empty ALL_FAIL_ITEM
    /// Groups by ProgramTestItemBin key and processes each group
    pub fn calculate(&self, sub_bin_failitems: Vec<SubBinFailitem>) -> Vec<BinFailitem> {
        // Filter records with ORDER_VALID_FLAG == 1 and non-empty ALL_FAIL_ITEM
        let filtered_items: Vec<SubBinFailitem> = sub_bin_failitems
            .into_iter()
            .filter(|item| {
                item.ORDER_VALID_FLAG == Some(1) && 
                item.ALL_FAIL_ITEM.as_ref().map_or(false, |items| !items.is_empty())
            })
            .collect();

        // Group by ProgramTestItemBin key
        let mut grouped: HashMap<ProgramTestItemBin, Vec<SubBinFailitem>> = HashMap::new();
        
        for item in filtered_items {
            let key = self.create_program_test_item_bin_key(&item);
            grouped.entry(key).or_insert_with(Vec::new).push(item);
        }

        // Process each group and calculate BinFailitem
        let mut results = Vec::new();
        for (_, group) in grouped {
            if let Some(first_item) = group.first() {
                let bin_failitem = self.common_service.calculate_bin_failitem(first_item);
                results.push(bin_failitem);
            }
        }

        results
    }

    /// Calculate bin relations from BinFailitem records
    /// Equivalent to calculateBinRelation function in Scala (without Spark Dataset operations)
    /// Groups by (TEST_PROGRAM, TEST_ITEM) and processes each group
    pub async fn calculate_bin_relation(&self, bin_failitems: Vec<BinFailitem>,
        mysql_config: MySqlConfig,
        lot_key: &LotKey,
        upload_type: &str,
        redis_provider: &redis_provider::provider::RedisProvider) {
        // Group by (TEST_PROGRAM, TEST_ITEM)
        let mut grouped: HashMap<(Option<String>, Option<String>), Vec<BinFailitem>> = HashMap::new();
        
        for item in bin_failitems {
            let key = (item.TEST_PROGRAM.clone(), item.TEST_ITEM.clone());
            grouped.entry(key).or_insert_with(Vec::new).push(item);
        }

        // Process each group and calculate bin relations
        let mut bin_relations = Vec::new();
        for (_, group) in grouped {
            if let Some(relation) = self.common_service.cal_bin_relation(&group) {
                bin_relations.push(relation);
            }
        }
        
        self.common_service.cal_test_program_test_plan(&bin_relations, redis_provider, mysql_config, lot_key, upload_type).await.unwrap();
    }

    /// Create ProgramTestItemBin key from SubBinFailitem
    /// This extracts the key fields used for grouping in the Scala version
    fn create_program_test_item_bin_key(&self, item: &SubBinFailitem) -> ProgramTestItemBin {
        let first_fail_item = item.FIRST_FAIL_ITEM.as_ref();
        
        ProgramTestItemBin {
            customer: item.CUSTOMER.clone().unwrap_or_default(),
            test_area: item.TEST_AREA.clone().unwrap_or_default(),
            factory: item.FACTORY.clone().unwrap_or_default(),
            test_stage: item.TEST_STAGE.clone().unwrap_or_default(),
            device_id: item.DEVICE_ID.clone().unwrap_or_default(),
            lot_type: item.LOT_TYPE.clone().unwrap_or_default(),
            test_program: item.TEST_PROGRAM.clone().unwrap_or_default(),
            test_program_version: item.TEST_PROGRAM_VERSION.clone().unwrap_or_default(),
            test_item: first_fail_item
                .and_then(|f| f.TEST_ITEM.clone())
                .unwrap_or_default(),
            hbin_num: item.HBIN_NUM,
            hbin_name: item.HBIN_NAM.clone().unwrap_or_default(),
            hbin_pf: item.HBIN_PF.clone().unwrap_or_default(),
            sbin_num: item.SBIN_NUM,
            sbin_name: item.SBIN_NAM.clone().unwrap_or_default(),
            sbin_pf: item.SBIN_PF.clone().unwrap_or_default(),
        }
    }
}



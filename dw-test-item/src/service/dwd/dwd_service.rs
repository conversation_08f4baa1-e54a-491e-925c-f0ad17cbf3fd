
use crate::config::DwTestItemConfig;
use ck_provider::{AsyncCkChannel, CkProviderImpl, CkStreamProcessorBuilder, StreamConfig};
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dto::dwd::test_item_detail_row::TestItemDetailRow;
use common::dwd::sink::test_item_detail_handler::TestItemDetailHandler;
use rayon::prelude::*;
use std::collections::HashMap;
use std::error::Error;
use std::sync::Arc;
use std::time::Duration;

pub async fn write_test_item_detail_to_clickhouse_concurrent(
    properties: DwTestItemConfig,
    test_item_detail: Vec<Vec<SubTestItemDetail>>,
    file_detail_map: HashMap<i64, FileDetail>,
    config: &DwTestItemConfig,
) -> Result<(Vec<Vec<SubTestItemDetail>>, HashMap<i64, FileDetail>), Box<dyn Error + Send + Sync>> {
    let ck_config = properties.get_ck_config(properties.dwd_db_name.as_str());
    let test_item_detail_handler =
        Arc::new(TestItemDetailHandler::new(properties.dwd_db_name.clone(), properties.insert_cluster_table));

    let db_table_name = format!("{}.{}", test_item_detail_handler.db_name, test_item_detail_handler.table_name);
    let batch_size = config.get_batch_size()?;

    // 配置流式处理参数 - 优化性能
    let total_batches = test_item_detail.len();
    let optimal_concurrent_flushes = std::cmp::min(total_batches * 2, 16);
    let optimal_buffer_size = std::cmp::max(batch_size * 4, 2000);

    let stream_config = StreamConfig::default()
        .with_buffer_size(optimal_buffer_size)
        .with_batch_size(batch_size)
        .with_flush_interval(Duration::from_millis(500))
        .with_max_retries(3)
        .with_backpressure_timeout(Duration::from_secs(600))
        .with_parallel_flush(true)
        .with_max_concurrent_flushes(optimal_concurrent_flushes);

    // 创建流式通道
    let (sender, receiver) = AsyncCkChannel::new::<TestItemDetailRow>(stream_config.clone(), 10);

    let ck_provider = CkProviderImpl::new(ck_config.clone());

    // 创建流处理器
    let mut processor = CkStreamProcessorBuilder::new()
        .with_receiver(receiver)
        .with_provider(ck_provider.clone())
        .with_config(stream_config)
        .with_table_name(db_table_name)
        .build()?;

    // 启动流处理器任务
    let processor_handle = tokio::spawn(async move {
        if let Err(e) = processor.start().await {
            eprintln!("流处理器错误: {:?}", e);
        }
    });

    // 直接在这里执行并发写入，避免数据传递和克隆
    let file_detail_map_arc = Arc::new(file_detail_map);
    let test_item_detail_arc = Arc::new(test_item_detail);

    // 创建并发任务处理每个批次
    let mut tasks = Vec::new();
    for batch_idx in 0..test_item_detail_arc.len() {
        let sender_clone = sender.clone();
        let file_detail_map_clone = file_detail_map_arc.clone();
        let test_item_detail_clone = test_item_detail_arc.clone();

        let task = tokio::spawn(async move {
            // 获取信号量许可

            let test_item_detail_batch = &test_item_detail_clone[batch_idx];
            let batch_start = std::time::Instant::now();
            log::info!("开始处理批次{}写入ClickHouse，数据量: {}", batch_idx, test_item_detail_batch.len());
            let mut build_duration_total = Duration::new(0, 0);
            let mut send_duration_total = Duration::new(0, 0);
            for chunck in test_item_detail_batch.chunks(10000) {
                let build_start = std::time::Instant::now();
                let batch_rows: Vec<TestItemDetailRow> = chunck
                    .par_iter()
                    .map(|item| {
                        let file_detail = file_detail_map_clone.get(&item.FILE_ID.unwrap()).unwrap();
                        TestItemDetailRow::new(item, file_detail)
                    })
                    .collect();
                let build_duration = build_start.elapsed();
                build_duration_total += build_duration;
                let send_start = std::time::Instant::now();
                sender_clone.send_batch(Some(batch_rows)).await?;
                let send_duration = send_start.elapsed();
                send_duration_total += send_duration;
            }

            let total_duration = batch_start.elapsed();
            log::info!(
                "完成批次{}写入ClickHouse，构建耗时: {:?}, 发送耗时: {:?}, 总耗时: {:?}",
                batch_idx,
                build_duration_total,
                send_duration_total,
                total_duration
            );
            Ok::<(), Box<dyn Error + Send + Sync>>(())
        });
        tasks.push(task);
    }

    // 等待所有任务完成
    for task in tasks {
        task.await??;
    }

    // 发送结束消息
    sender.send(None).await?;

    // 等待处理器完成
    processor_handle.await?;

    // 从Arc中提取原始数据返回
    let file_detail_map = Arc::try_unwrap(file_detail_map_arc).map_err(|_| "Failed to unwrap Arc<HashMap>")?;
    let test_item_detail =
        Arc::try_unwrap(test_item_detail_arc).map_err(|_| "Failed to unwrap Arc<Vec<Vec<SubTestItemDetail>>>")?;

    Ok((test_item_detail, file_detail_map))
}


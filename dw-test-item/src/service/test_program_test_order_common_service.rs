use std::collections::HashMap;
use chrono::{DateTime, Utc};
use serde_json;
use log::info;

use common::dws::dws_service::{DwsSubDieDetail, DwsSubTestItemDetail};
use common::dto::dws::test_program_test_order::TestProgramTestOrder;
use common::dws::model::mysql::dw_test_program_test_plan::DwTestProgramTestPlan;
use common::model::constant::{COMMA, EMPTY, SYSTEM};
use common::model::key::lot_key::LotKey;
use common::repository::mysql::test_program_test_plan_repository::TestProgramTestPlanRepository;
use common::repository::ck::test_program_test_order_repository::TestProgramTestOrderRepository;
use mysql_provider::MySqlConfig;
use ck_provider::CkConfig;

const PF_PASS: &str = "P";

#[derive(Debug, C<PERSON>)]
pub struct TestProgramTestOrderCommonService;

impl TestProgramTestOrderCommonService {
    pub fn new() -> Self {
        Self
    }

    /// Filter pass bin dies
    /// Equivalent to filterPassBin in Scala
    pub fn filter_pass_bin(&self, die: &DwsSubDieDetail, cal_test_program_test_order_programs: &[String]) -> bool {
        die.HBIN_PF == PF_PASS && cal_test_program_test_order_programs.contains(&die.TEST_PROGRAM)
    }

    /// Find first die in file
    /// Equivalent to findFileFirstDie in Scala
    pub fn find_file_first_die(&self, dies: &[&DwsSubDieDetail]) -> Option<(u32, String, u32, Option<chrono::DateTime<chrono::Utc>>)> {
        dies.iter()
            .min_by_key(|die| die.C_PART_ID.unwrap_or(u32::MAX))
            .map(|die| {
                (
                    die.FILE_ID,
                    die.TEST_PROGRAM.clone(),
                    die.C_PART_ID.unwrap_or(0),
                    die.START_TIME,
                )
            })
    }

    /// Find first die for each program
    /// Equivalent to findProgramFirstDie in Scala
    pub fn find_program_first_die(&self, file_first_dies: &[(u32, String, u32, Option<chrono::DateTime<chrono::Utc>>)]) -> HashMap<String, (u32, String, u32, Option<chrono::DateTime<chrono::Utc>>)> {
        let mut program_first_dies: HashMap<String, (u32, String, u32, Option<chrono::DateTime<chrono::Utc>>)> = HashMap::new();
        
        for die in file_first_dies {
            let program = &die.1;
            match program_first_dies.get(program) {
                Some(existing) => {
                    // Compare start_time - handle Option<DateTime>
                    let should_replace = match (die.3, existing.3) {
                        (Some(die_time), Some(existing_time)) => die_time < existing_time,
                        (Some(_), None) => true,
                        _ => false,
                    };
                    if should_replace {
                        program_first_dies.insert(program.clone(), die.clone());
                    }
                }
                None => {
                    program_first_dies.insert(program.clone(), die.clone());
                }
            }
        }
        
        program_first_dies
    }

    /// Predict if this is the first die test for the program
    /// Equivalent to predictProgramFirstdieTest in Scala
    pub fn predict_program_first_die_test(&self, test_item: &DwsSubTestItemDetail, first_die: Option<&(u32, String, u32, Option<chrono::DateTime<chrono::Utc>>)>) -> bool {
        match first_die {
            Some(die) => {
                test_item.FILE_ID == Some(die.0 as i64) && test_item.C_PART_ID == Some(die.2 as i64)
            }
            None => false,
        }
    }

    /// Generate TestProgramTestOrder from DwsSubTestItemDetail
    /// Equivalent to generateTestProgramTestOrder in Scala
    pub fn generate_test_program_test_order(&self, test_item: &DwsSubTestItemDetail) -> TestProgramTestOrder {
        let now = chrono::Utc::now().timestamp_millis();
        let create_hour_key = self.get_day_hour(now);
        let create_day_key = self.get_day(now);

        // Build extra_info map based on test area - get values directly from DWS test_item
        let extra_info = if self.is_cp_test_area(&test_item.TEST_AREA) {
            let mut map = HashMap::new();
            map.insert("LOT_ID".to_string(), test_item.LOT_ID.clone());
            map.insert("WAFER_NO".to_string(), test_item.WAFER_NO.clone());
            Some(map)
        } else {
            let mut map = HashMap::new();
            map.insert("LOT_ID".to_string(), test_item.LOT_ID.clone());
            Some(map)
        };
        
        TestProgramTestOrder {
            CUSTOMER: Some(test_item.CUSTOMER.clone()),
            SUB_CUSTOMER: Some(test_item.SUB_CUSTOMER.clone()),
            UPLOAD_TYPE: Some(test_item.UPLOAD_TYPE.clone()),
            FACTORY: Some(test_item.FACTORY.clone()),
            FACTORY_SITE: Some(test_item.FACTORY_SITE.clone()),
            FAB: Some(test_item.FAB.clone()),
            FAB_SITE: Some(test_item.FAB_SITE.clone()),
            TEST_AREA: Some(test_item.TEST_AREA.clone()),
            TEST_STAGE: Some(test_item.TEST_STAGE.clone()),
            DEVICE_ID: Some(test_item.DEVICE_ID.clone()),
            LOT_TYPE: Some(test_item.LOT_TYPE.clone()),
            TEST_PROGRAM: Some(test_item.TEST_PROGRAM.clone()),
            TEST_PROGRAM_VERSION: Some(test_item.TEST_PROGRAM_VERSION.clone()),
            TEST_NUM: test_item.TEST_NUM,
            TEST_TXT: test_item.TEST_TXT.clone().into(),
            TEST_ITEM: test_item.TEST_ITEM.clone().into(),
            TESTITEM_TYPE: test_item.TESTITEM_TYPE.clone().into(),
            TEST_ORDER: test_item.TEST_ORDER,
            EXTRA_INFO: extra_info,
            CREATE_HOUR_KEY: Some(create_hour_key),
            CREATE_DAY_KEY: Some(create_day_key),
            CREATE_TIME: Some(now),
            CREATE_USER: Some(SYSTEM.to_string()),
            UPLOAD_TIME: test_item.START_TIME.map(|t| t.timestamp_millis()),
        }
    }

    /// Calculate test order for records
    /// Equivalent to calTestOrder in Scala
    pub fn cal_test_order(&self, records: &[TestProgramTestOrder]) -> HashMap<String, HashMap<String, TestProgramTestOrder>> {
        let mut program_map = HashMap::new();
        
        // Group by test program
        for record in records {
            let program = record.TEST_PROGRAM.as_ref().unwrap_or(&String::new()).clone();
            program_map.entry(program).or_insert_with(Vec::new).push(record.clone());
        }
        
        // Calculate test order for each program
        let mut result = HashMap::new();
        for (program, mut items) in program_map {
            // Sort by existing test order
            items.sort_by_key(|item| item.TEST_ORDER.unwrap_or(0));
            
            let mut test_item_map = HashMap::new();
            let mut order = 0i64;
            
            for mut item in items {
                let test_item = item.TEST_ITEM.as_ref().unwrap_or(&String::new()).clone();
                if !test_item_map.contains_key(&test_item) {
                    order += 1;
                    item.TEST_ORDER = Some(order as i64);
                    test_item_map.insert(test_item, item);
                }
            }
            
            result.insert(program, test_item_map);
        }
        
        result
    }

    /// Write test program test order data
    /// Equivalent to writeTestProgramTestOrder in Scala
    pub async fn write_test_program_test_order(
        &self,
        program_with_test_item_map: &HashMap<String, HashMap<String, TestProgramTestOrder>>,
        dim_db_name: &str,
        mysql_config: MySqlConfig,
        ck_config: CkConfig,
        redis_provider: &redis_provider::provider::RedisProvider,
        lot_key: &LotKey,
        upload_type: &str,
        write_hdfs_and_ck: impl Fn(bool, &[TestProgramTestOrder]),
    ) -> Result<(), Box<dyn std::error::Error>> {
        let test_program_test_order_repository = TestProgramTestOrderRepository::new(
            dim_db_name.to_string(),
            ck_config,
        );
        let mut hdfs_first_insert_flag = true;
        
        for (program, test_item_map) in program_with_test_item_map {
            let mut test_program_test_order_lock = redis_provider.get_test_program_test_order_lock(
                lot_key,
                upload_type,
                program,
            );
            
            if test_program_test_order_lock.try_lock()? {
                let lock_key = test_program_test_order_lock.key();
                
                // Use a closure to ensure unlock is called even if an error occurs
                let result = async {
                    let can_init_programs = test_program_test_order_repository
                        .can_init_test_program_test_order(lot_key, upload_type, &[program.clone()])
                        .await?;
                    
                    if can_init_programs.contains(program) {
                        info!("{} 可以写入", lock_key);
                        
                        // Calculate test program test plan
                        self.cal_test_program_test_plan(
                            mysql_config.clone(),
                            redis_provider,
                            lot_key,
                            upload_type,
                            program,
                            test_item_map,
                        ).await?;
                        
                        // Write to HDFS and ClickHouse
                        let values: Vec<TestProgramTestOrder> = test_item_map.values().cloned().collect();
                        write_hdfs_and_ck(hdfs_first_insert_flag, &values);
                        
                        hdfs_first_insert_flag = false;
                    } else {
                        info!("{} 已经被写入，跳过写入", lock_key);
                    }
                    
                    Ok::<(), Box<dyn std::error::Error>>(())
                }.await;
                
                // Always unlock
                test_program_test_order_lock.unlock()?;
                
                // Propagate any error that occurred
                result?;
            } else {
                let lock_key = test_program_test_order_lock.key();
                info!("{} 上锁失败，跳过写入", lock_key);
            }
        }
        
        Ok(())
    }

    /// Calculate test program test plan
    /// Equivalent to calTestProgramTestPlan in Scala
    async fn cal_test_program_test_plan(
        &self,
        mysql_config: MySqlConfig,
        redis_provider: &redis_provider::provider::RedisProvider,
        lot_key: &LotKey,
        upload_type: &str,
        program: &str,
        item_with_test_order_map: &HashMap<String, TestProgramTestOrder>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let test_program_test_plan_repository = TestProgramTestPlanRepository::new(mysql_config).await?;
        
        let can_init_programs = test_program_test_plan_repository
            .can_init_test_program_test_plan_test_order(lot_key, upload_type, &[program.to_string()])
            .await?;
        
        if can_init_programs.contains(&program.to_string()) {
            let mut test_program_test_plan_lock = redis_provider.get_test_program_test_plan_lock(
                lot_key,
                upload_type,
                program,
            );
            
            // Acquire lock
            test_program_test_plan_lock.lock()?;
            
            let lock_key = test_program_test_plan_lock.key();
            
            // Use a closure to ensure unlock is called even if an error occurs
            let result = async {
                let exists_program_test_plan_map = test_program_test_plan_repository
                    .query_program_plan(lot_key,upload_type, program)
                    .await?;
                
                // Check if test_order data already exists
                let has_test_order_data = exists_program_test_plan_map.values().any(|plan| {
                    plan.test_order_manual_import_flag.unwrap_or(0) == 1 || plan.test_order.is_some()
                });
                
                if has_test_order_data {
                    info!("{} 存在test_order数据，跳过写入", lock_key);
                } else {
                    info!("{} 可以写入test_order数据", lock_key);
                    self.write_test_program_test_plan(
                        item_with_test_order_map,
                        &exists_program_test_plan_map,
                        &test_program_test_plan_repository,
                    ).await?;
                }
                
                Ok::<(), Box<dyn std::error::Error>>(())
            }.await;
            
            // Always unlock
            test_program_test_plan_lock.unlock()?;
            
            // Propagate any error that occurred
            result?;
        }
        
        Ok(())
    }

    /// Write test program test plan data
    /// Equivalent to writeTestProgramTestPlan in Scala
    async fn write_test_program_test_plan(
        &self,
        item_with_test_order_map: &HashMap<String, TestProgramTestOrder>,
        exists_program_test_plan_map: &HashMap<String, DwTestProgramTestPlan>,
        test_program_test_plan_repository: &TestProgramTestPlanRepository,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let now = chrono::Utc::now().naive_utc();
        
        let mut update_plans = Vec::new();
        let mut insert_plans = Vec::new();
        
        // Update existing plans
        for (test_item, order_data) in item_with_test_order_map {
            if let Some(mut existing_plan) = exists_program_test_plan_map.get(test_item).cloned() {
                // Update FAB and FAB_SITE if not blank
                if let Some(fab) = &order_data.FAB {
                    if !fab.trim().is_empty() {
                        existing_plan.fab = Some(fab.clone());
                    }
                }
                if let Some(fab_site) = &order_data.FAB_SITE {
                    if !fab_site.trim().is_empty() {
                        existing_plan.fab_site = Some(fab_site.clone());
                    }
                }
                
                existing_plan.test_order = order_data.TEST_ORDER.map(|o| o as i64);
                existing_plan.update_user = Some(SYSTEM.to_string());
                existing_plan.update_time = Some(now);
                
                update_plans.push(existing_plan);
            }
        }
        
        // Insert new plans
        for (test_item, order_data) in item_with_test_order_map {
            if !exists_program_test_plan_map.contains_key(test_item) {
                let new_plan = DwTestProgramTestPlan {
                    id: None,
                    customer: order_data.CUSTOMER.clone(),
                    sub_customer: order_data.SUB_CUSTOMER.clone(),
                    upload_type: order_data.UPLOAD_TYPE.clone(),
                    test_area: order_data.TEST_AREA.clone(),
                    factory: order_data.FACTORY.clone(),
                    factory_site: order_data.FACTORY_SITE.clone(),
                    fab: order_data.FAB.clone(),
                    fab_site: order_data.FAB_SITE.clone(),
                    device_id: order_data.DEVICE_ID.clone(),
                    test_stage: order_data.TEST_STAGE.clone(),
                    lot_type: order_data.LOT_TYPE.clone(),
                    test_program: order_data.TEST_PROGRAM.clone(),
                    test_item: order_data.TEST_ITEM.clone(),
                    test_order: order_data.TEST_ORDER.map(|o| o as i64),
                    testitem_type: order_data.TESTITEM_TYPE.clone(),
                    test_num: order_data.TEST_NUM.map(|n| n as i64),
                    test_txt: order_data.TEST_TXT.clone(),
                    bin_relation: Some("[]".to_string()), // EMPTY_JSON_ARRAY
                    hbins: Some(EMPTY.to_string()),
                    sbins: Some(EMPTY.to_string()),
                    unit_scale: None,
                    custom_unit: Some(EMPTY.to_string()),
                    test_order_manual_import_flag: Some(0),
                    bin_relation_manual_import_flag: Some(0),
                    create_time: Some(now),
                    update_time: Some(now),
                    create_user: Some(SYSTEM.to_string()),
                    update_user: Some(SYSTEM.to_string()),
                };
                
                insert_plans.push(new_plan);
            }
        }
        
        // Save all plans
        let mut all_plans = insert_plans;
        all_plans.extend(update_plans);
        
        if !all_plans.is_empty() {
            test_program_test_plan_repository.save_or_update_test_program_test_plan(all_plans).await?;
        }
        
        Ok(())
    }

    /// Helper function to check if test area is CP
    fn is_cp_test_area(&self, test_area: &str) -> bool {
        // This should match the SUPPORT_CP_TEST_AREA_LIST logic from Scala
        test_area == "CP" || test_area.starts_with("CP_")
    }

    /// Helper function to format day hour
    fn get_day_hour(&self, timestamp: i64) -> String {
        let dt = chrono::DateTime::from_timestamp_millis(timestamp)
            .unwrap_or_else(|| chrono::Utc::now());
        dt.format("%Y%m%d%H").to_string()
    }

    /// Helper function to format day
    fn get_day(&self, timestamp: i64) -> String {
        let dt = chrono::DateTime::from_timestamp_millis(timestamp)
            .unwrap_or_else(|| chrono::Utc::now());
        dt.format("%Y%m%d").to_string()
    }
}
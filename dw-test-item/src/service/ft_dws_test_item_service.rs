//! FT DWS测试项服务实现
//!
//! 实现FT工艺的DWS层测试项数据处理逻辑

use log::{info, error};
use anyhow::Result;
use std::collections::HashMap;
use std::error::Error;

// 从common模块导入所需结构体
use common::dto::dwd::die_detail_parquet::DieDetailParquet;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dws::dws_service::{DwsService, DwsSubDieDetail, DwsSubFileDetail, DwsSubTestItemDetail};
use common::dws::table::distributed::bin_test_item_index_service::BinTestItemIndexService;
use common::dws::table::distributed::site_test_item_index_service::SiteTestItemIndexService;
use common::dws::table::distributed::test_item_index_service::TestItemIndexService;
use common::ck::ck_sink::CkSink;
use common::utils::path;
use common::utils::ck_util::CkUtil;
use common::model::key::wafer_key::WaferKey;
use parquet_provider::parquet_provider::write_parquet_multi;
use crate::config::DwTestItemConfig;

// 从common::dws::model导入索引结构体
use common::dws::model::{BinTestItemIndex, BinTestItemIndexRow, SiteTestItemIndex, SiteTestItemIndexRow, TestItemIndex, TestItemIndexRow};

use ck_provider::{CkProvider, CkProviderImpl};



/// FT DWS测试项服务
pub struct FtDwsTestItemService {
    // DWS结果分区数
    dws_result_partition: i32,
    // 测试区域
    test_area: String,
    // 配置
    properties: DwTestItemConfig,
}

impl FtDwsTestItemService {
    /// 创建新的FT DWS测试项服务实例
    pub fn new(dws_result_partition: i32, test_area: String, properties: DwTestItemConfig) -> Self {
        Self {
            dws_result_partition,
            test_area,
            properties,
        }
    }

    /// 构建SubFileDetail映射 (对应Scala中的FileDetailService.broadcastSubFileDetail)
    fn build_sub_file_detail_map(&self, dws_die_detail: &[DwsSubDieDetail]) -> std::collections::HashMap<u32, DwsSubFileDetail> {
        use std::collections::HashMap;
        
        let mut file_detail_map = HashMap::new();
        
        // 按FILE_ID分组，每个FILE_ID取第一个记录来构建SubFileDetail
        for die in dws_die_detail {
            if !file_detail_map.contains_key(&die.FILE_ID) {
                let sub_file_detail = DwsSubFileDetail {
                    CUSTOMER: die.CUSTOMER.clone(),
                    SUB_CUSTOMER: die.SUB_CUSTOMER.clone(),
                    UPLOAD_TYPE: die.UPLOAD_TYPE.clone(),
                    FILE_ID: die.FILE_ID,
                    FILE_NAME: die.FILE_NAME.clone(),
                    FILE_TYPE: die.FILE_TYPE.clone(),
                    FACTORY: die.FACTORY.clone(),
                    FACTORY_SITE: die.FACTORY_SITE.clone(),
                    FAB: die.FAB.clone(),
                    FAB_SITE: die.FAB_SITE.clone(),
                    LOT_TYPE: die.LOT_TYPE.clone(),
                    TEST_AREA: die.TEST_AREA.clone(),
                    OFFLINE_RETEST: die.OFFLINE_RETEST.map(|v| v as i32),
                    INTERRUPT: die.INTERRUPT.map(|v| v as i32),
                    DUP_RETEST: die.DUP_RETEST.map(|v| v as i32),
                    BATCH_NUM: die.BATCH_NUM.map(|v| v as i32),
                    LOT_ID: die.LOT_ID.clone(),
                    SBLOT_ID: die.SBLOT_ID.clone(),
                    PROBER_HANDLER_ID: die.PROBER_HANDLER_ID.clone(),
                    TESTER_TYPE: die.TESTER_TYPE.clone(),
                    TEST_STAGE: die.TEST_STAGE.clone(),
                    DEVICE_ID: die.DEVICE_ID.clone(),
                    TEST_PROGRAM: die.TEST_PROGRAM.clone(),
                    TEST_TEMPERATURE: die.TEST_TEMPERATURE.clone(),
                    TEST_PROGRAM_VERSION: die.TEST_PROGRAM_VERSION.clone(),
                    TESTER_NAME: die.TESTER_NAME.clone(),
                    PROBECARD_LOADBOARD_ID: die.PROBECARD_LOADBOARD_ID.clone(),
                    START_TIME: die.START_TIME,
                    END_TIME: die.END_TIME,
                    START_HOUR_KEY: die.START_HOUR_KEY.clone(),
                    START_DAY_KEY: die.START_DAY_KEY.clone(),
                    END_HOUR_KEY: die.END_HOUR_KEY.clone(),
                    END_DAY_KEY: die.END_DAY_KEY.clone(),
                    FLOW_ID: die.FLOW_ID.clone(),
                    RETEST_BIN_NUM: die.RETEST_BIN_NUM.clone(),
                    PROCESS: die.PROCESS.clone(),
                    UPLOAD_TIME: die.UPLOAD_TIME,
                };
                file_detail_map.insert(die.FILE_ID, sub_file_detail);
            }
        }
        
        file_detail_map
    }

    /// 填充文件信息到测试项详情 (对应Scala中的fillFileInfo方法)
    fn fill_file_info(&self, test_item_detail: &mut DwsSubTestItemDetail, file_detail: &DwsSubFileDetail) {
        test_item_detail.CUSTOMER = file_detail.CUSTOMER.clone();
        test_item_detail.SUB_CUSTOMER = file_detail.SUB_CUSTOMER.clone();
        test_item_detail.UPLOAD_TYPE = file_detail.UPLOAD_TYPE.clone();
        test_item_detail.FILE_ID = Some(file_detail.FILE_ID as i64);
        test_item_detail.FILE_NAME = file_detail.FILE_NAME.clone();
        test_item_detail.FILE_TYPE = file_detail.FILE_TYPE.clone();
        test_item_detail.FACTORY = file_detail.FACTORY.clone();
        test_item_detail.FACTORY_SITE = file_detail.FACTORY_SITE.clone();
        test_item_detail.FAB = file_detail.FAB.clone();
        test_item_detail.FAB_SITE = file_detail.FAB_SITE.clone();
        test_item_detail.LOT_TYPE = file_detail.LOT_TYPE.clone();
        test_item_detail.TEST_AREA = file_detail.TEST_AREA.clone();
        test_item_detail.OFFLINE_RETEST = file_detail.OFFLINE_RETEST;
        test_item_detail.INTERRUPT = file_detail.INTERRUPT;
        test_item_detail.DUP_RETEST = file_detail.DUP_RETEST;
        test_item_detail.BATCH_NUM = file_detail.BATCH_NUM;
        test_item_detail.LOT_ID = file_detail.LOT_ID.clone();
        test_item_detail.SBLOT_ID = file_detail.SBLOT_ID.clone();
        test_item_detail.PROBER_HANDLER_ID = file_detail.PROBER_HANDLER_ID.clone();
        test_item_detail.TESTER_TYPE = file_detail.TESTER_TYPE.clone();
        test_item_detail.TEST_STAGE = file_detail.TEST_STAGE.clone();
        test_item_detail.DEVICE_ID = file_detail.DEVICE_ID.clone();
        test_item_detail.TEST_PROGRAM = file_detail.TEST_PROGRAM.clone();
        test_item_detail.TEST_TEMPERATURE = file_detail.TEST_TEMPERATURE.clone();
        test_item_detail.TEST_PROGRAM_VERSION = file_detail.TEST_PROGRAM_VERSION.clone();
        test_item_detail.TESTER_NAME = file_detail.TESTER_NAME.clone();
        test_item_detail.PROBECARD_LOADBOARD_ID = file_detail.PROBECARD_LOADBOARD_ID.clone();
        test_item_detail.START_TIME = file_detail.START_TIME;
        test_item_detail.END_TIME = file_detail.END_TIME;
        test_item_detail.START_HOUR_KEY = file_detail.START_HOUR_KEY.clone();
        test_item_detail.START_DAY_KEY = file_detail.START_DAY_KEY.clone();
        test_item_detail.END_HOUR_KEY = file_detail.END_HOUR_KEY.clone();
        test_item_detail.END_DAY_KEY = file_detail.END_DAY_KEY.clone();
        test_item_detail.FLOW_ID = file_detail.FLOW_ID.clone();
        test_item_detail.RETEST_BIN_NUM = file_detail.RETEST_BIN_NUM.clone();
        test_item_detail.PROCESS = file_detail.PROCESS.clone();
        test_item_detail.UPLOAD_TIME = file_detail.UPLOAD_TIME;
    }
    

    
    /// 计算DWS层测试项数据
    pub async fn calculate(
        &self,
        die_detail: Vec<DieDetailParquet>,
        test_item_detail: Vec<SubTestItemDetail>,
        wafer_key: &WaferKey,
        test_area: &str,
    ) -> Result<(), Box<dyn Error>> {
        info!("Calculating FT DWS test item for lot: {}", wafer_key.lot_id);
        
        // 构建DWS子Die详情 - FT工艺特殊处理，需要处理retest_bin_num
        let dws_die_detail: Vec<DwsSubDieDetail> = die_detail
            .iter()
            .map(|item| DwsService::build_dws_sub_die_detail_from_parquet(item))
            .collect();

        // 构建文件详情映射 (对应Scala中的fileDetailMap)
        let file_detail_map = self.build_sub_file_detail_map(&dws_die_detail);
            
        // FT工艺只过滤TEST_VALUE，不过滤坐标
        let dws_test_item_detail: Vec<DwsSubTestItemDetail> = test_item_detail
            .iter()
            .filter(|item| {
                item.TEST_VALUE.is_some() && 
                item.TEST_VALUE.unwrap().is_finite()
            })
            .map(|item| {
                let mut dws_item = DwsService::build_dws_sub_test_item_detail(item);
                // 填充文件信息 (对应Scala中的fillFileInfo)
                if let Some(file_id) = item.FILE_ID {
                    if let Some(file_detail) = file_detail_map.get(&(file_id as u32)) {
                        self.fill_file_info(&mut dws_item, file_detail);
                    }
                }
                dws_item
            })
            .collect();

        // 计算Bin测试项索引
        self.calculate_bin_test_item_index(&dws_test_item_detail, test_area, wafer_key).await?;
        
        // 计算站点测试项索引
        self.calculate_site_test_item_index(&dws_test_item_detail, test_area, wafer_key).await?;
        
        // 计算测试项索引
        self.calculate_test_item_index(&dws_test_item_detail, test_area, wafer_key).await?;
        
        info!("FT DWS calculation completed for lot: {}", wafer_key.lot_id);
        Ok(())
    }
    
    /// 计算Bin测试项索引
    async fn calculate_bin_test_item_index(
        &self, 
        dws_test_item_detail: &[DwsSubTestItemDetail], 
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error>> {
        info!("Calculating bin test item index for {} items", dws_test_item_detail.len());
        
        let bin_test_item_service = BinTestItemIndexService::new(test_area.to_string());
        // FT工艺需要retest_bin_num_map参数，根据Scala代码构建
        let retest_bin_num_map = HashMap::new(); // 简化处理，实际项目中需要构建这个映射
        let bin_test_item_indices = bin_test_item_service.calculate(dws_test_item_detail, Some(&retest_bin_num_map));
        
        info!("Generated {} bin test item indices", bin_test_item_indices.len());
        
        if !bin_test_item_indices.is_empty() {
            // 写入HDFS (Parquet文件)
            self.write_bin_test_item_index_to_hdfs(&bin_test_item_indices, test_area, wafer_key).await?;
            
            // 写入ClickHouse
            self.write_bin_test_item_index_to_clickhouse(&bin_test_item_indices, wafer_key, test_area).await?;
        }
        
        Ok(())
    }
    
    /// 计算站点测试项索引
    async fn calculate_site_test_item_index(
        &self, 
        dws_test_item_detail: &[DwsSubTestItemDetail],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error>> {
        info!("Calculating site test item index for {} items", dws_test_item_detail.len());
        
        let site_test_item_service = SiteTestItemIndexService::new(test_area.to_string());
        let site_test_item_indices = site_test_item_service.calculate(dws_test_item_detail);
        
        info!("Generated {} site test item indices", site_test_item_indices.len());
        
        if !site_test_item_indices.is_empty() {
            // 写入HDFS (Parquet文件)
            self.write_site_test_item_index_to_hdfs(&site_test_item_indices, test_area, wafer_key).await?;
            
            // 写入ClickHouse
            self.write_site_test_item_index_to_clickhouse(&site_test_item_indices).await?;
        }
        
        Ok(())
    }
    
    /// 计算测试项索引
    async fn calculate_test_item_index(
        &self, 
        dws_test_item_detail: &[DwsSubTestItemDetail],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error>> {
        info!("Calculating test item index for {} items", dws_test_item_detail.len());
        
        let test_item_service = TestItemIndexService::new(test_area.to_string());
        let test_item_indices = test_item_service.calculate(dws_test_item_detail);
        
        info!("Generated {} test item indices", test_item_indices.len());
        
        if !test_item_indices.is_empty() {
            // 写入HDFS (Parquet文件)
            self.write_test_item_index_to_hdfs(&test_item_indices, test_area, wafer_key).await?;
            
            // 写入ClickHouse
            self.write_test_item_index_to_clickhouse(&test_item_indices, wafer_key, test_area).await?;
        }
        
        Ok(())
    }

    // ========== HDFS写入方法 ==========
    
    /// 写入BinTestItemIndex到HDFS
    async fn write_bin_test_item_index_to_hdfs(
        &self,
        data: &[BinTestItemIndex],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error>> {
        // FT工艺使用lot路径，不使用wafer_no
        let table_path = path::get_lot_path(
            &self.properties.ft_dws_result_dir_template,
            "dws_bin_test_item_index",
            test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );
        
        info!("写入BinTestItemIndex parquet文件到路径: {}", table_path);
        
        // 使用与DWD相同的写入方式，直接写入HDFS
        // 将数据包装成Vec<Vec<T>>格式以匹配write_parquet_multi的期望
        let data_vec = data.to_vec();
        let data_batches = vec![data_vec];
        let data_refs: Vec<&Vec<BinTestItemIndex>> = data_batches.iter().collect();
        write_parquet_multi(
            &table_path,
            &data_refs,
            Some(&parquet_provider::hdfs_provider::HdfsConfig::default()),
            1000, // batch_size
        )
        .await
        .map_err(|e| Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("写入parquet文件失败: {}", e))) as Box<dyn Error>)?;
        info!("成功写入BinTestItemIndex parquet文件到: {}/bin_test_item_index.parquet", table_path);
        
        Ok(())
    }
    
    /// 写入SiteTestItemIndex到HDFS
    async fn write_site_test_item_index_to_hdfs(
        &self,
        data: &[SiteTestItemIndex],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error>> {
        // FT工艺使用lot路径，不使用wafer_no
        let table_path = path::get_lot_path(
            &self.properties.ft_dws_result_dir_template,
            "dws_site_test_item_index",
            test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );
        
        info!("写入SiteTestItemIndex parquet文件到路径: {}", table_path);
        
        // 使用与DWD相同的写入方式，直接写入HDFS
        // 将数据包装成Vec<Vec<T>>格式以匹配write_parquet_multi的期望
        let data_vec = data.to_vec();
        let data_batches = vec![data_vec];
        let data_refs: Vec<&Vec<SiteTestItemIndex>> = data_batches.iter().collect();
        write_parquet_multi(
            &table_path,
            &data_refs,
            Some(&parquet_provider::hdfs_provider::HdfsConfig::default()),
            1000, // batch_size
        )
        .await
        .map_err(|e| Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("写入parquet文件失败: {}", e))) as Box<dyn Error>)?;
        info!("成功写入SiteTestItemIndex parquet文件到: {}/site_test_item_index.parquet", table_path);
        
        Ok(())
    }
    
    /// 写入TestItemIndex到HDFS
    async fn write_test_item_index_to_hdfs(
        &self,
        data: &[TestItemIndex],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error>> {
        // FT工艺使用lot路径，不使用wafer_no
        let table_path = path::get_lot_path(
            &self.properties.ft_dws_result_dir_template,
            "dws_test_item_index",
            test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );
        
        info!("写入TestItemIndex parquet文件到路径: {}", table_path);
        
        // 使用与DWD相同的写入方式，直接写入HDFS
        // 将数据包装成Vec<Vec<T>>格式以匹配write_parquet_multi的期望
        let data_vec = data.to_vec();
        let data_batches = vec![data_vec];
        let data_refs: Vec<&Vec<TestItemIndex>> = data_batches.iter().collect();
        write_parquet_multi(
            &table_path,
            &data_refs,
            Some(&parquet_provider::hdfs_provider::HdfsConfig::default()),
            1000, // batch_size
        )
        .await
        .map_err(|e| Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("写入parquet文件失败: {}", e))) as Box<dyn Error>)?;
        info!("成功写入TestItemIndex parquet文件到: {}/test_item_index.parquet", table_path);
        
        Ok(())
    }
    
    // ========== ClickHouse写入方法 ==========
    
    /// 写入BinTestItemIndex到ClickHouse - 转换为ClickHouse实体类
    async fn write_bin_test_item_index_to_clickhouse(
        &self,
        data: &[BinTestItemIndex],
        _wafer_key: &WaferKey,
        _test_area: &str,
    ) -> Result<(), Box<dyn Error>> {
        let ck_config = self.properties.get_ck_config(&self.properties.dws_db_name);
        let ck_provider = CkProviderImpl::new(ck_config.clone());

        info!("开始写入BinTestItemIndex到ClickHouse，数据量: {}", data.len());

        // 转换为ClickHouse实体类
        let clickhouse_rows: Vec<BinTestItemIndexRow> = data
            .iter()
            .map(|hdfs_entity| BinTestItemIndexRow::from_hdfs_entity(hdfs_entity))
            .collect();

        // 使用批量写入方式
        let batch_size = 1000;
        let mut total_written = 0;

        for (batch_idx, chunk) in clickhouse_rows.chunks(batch_size).enumerate() {
            info!("写入第{}批BinTestItemIndex数据，数量: {}", batch_idx + 1, chunk.len());
            
            match ck_provider.insert("dws.dws_bin_test_item_index_cluster", chunk).await {
                Ok(_) => {
                    total_written += chunk.len();
                    info!("成功写入第{}批数据，累计: {}/{}", batch_idx + 1, total_written, clickhouse_rows.len());
                }
                Err(e) => {
                    error!("写入第{}批BinTestItemIndex数据失败: {}", batch_idx + 1, e);
                    return Err(Box::new(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        format!("写入第{}批数据失败: {}", batch_idx + 1, e),
                    )));
                }
            }
        }

        info!("成功写入所有BinTestItemIndex到ClickHouse，总数: {}", total_written);
        Ok(())
    }
    
    /// 写入SiteTestItemIndex到ClickHouse - 使用流式处理避免内存溢出
    async fn write_site_test_item_index_to_clickhouse(
        &self,
        data: &[SiteTestItemIndex],
    ) -> Result<(), Box<dyn Error>> {
        use ck_provider::{AsyncCkChannel, CkProviderImpl, CkStreamProcessorBuilder, StreamConfig};
        use std::time::Duration;

        let ck_config = self.properties.get_ck_config(&self.properties.dws_db_name);
        let db_table_name = format!("{}.dws_site_test_item_index_local", self.properties.dws_db_name);

        info!("开始写入SiteTestItemIndex到ClickHouse，数据量: {}", data.len());

        // 配置流式处理参数 - 小批量处理避免内存问题
        let batch_size = 1000; // 减小批次大小
        let stream_config = StreamConfig::default()
            .with_buffer_size(2000)
            .with_batch_size(batch_size)
            .with_flush_interval(Duration::from_millis(1000))
            .with_max_retries(3)
            .with_backpressure_timeout(Duration::from_secs(300))
            .with_parallel_flush(false) // 禁用并行刷新减少内存使用
            .with_max_concurrent_flushes(1);

        // 创建流式通道
        let (sender, receiver) = AsyncCkChannel::new::<SiteTestItemIndex>(stream_config.clone(), 5);

        let ck_provider = CkProviderImpl::new(ck_config.clone());

        // 创建流处理器
        let mut processor = CkStreamProcessorBuilder::new()
            .with_receiver(receiver)
            .with_provider(ck_provider.clone())
            .with_config(stream_config)
            .with_table_name(db_table_name)
            .build()?;

        // 启动流处理器任务
        let processor_handle = tokio::spawn(async move {
            if let Err(e) = processor.start().await {
                eprintln!("SiteTestItemIndex流处理器错误: {:?}", e);
            }
        });

        // 分批发送数据
        for chunk in data.chunks(batch_size) {
            sender.send_batch(Some(chunk.to_vec())).await?;
        }

        // 发送结束消息
        sender.send(None).await?;

        // 等待处理器完成
        processor_handle.await?;

        info!("成功写入SiteTestItemIndex到ClickHouse");
        Ok(())
    }
    
    /// 写入TestItemIndex到ClickHouse - 使用流式处理避免内存溢出
    async fn write_test_item_index_to_clickhouse(
        &self,
        data: &[TestItemIndex],
        wafer_key: &WaferKey,
        test_area: &str,
    ) -> Result<(), Box<dyn Error>> {
        use ck_provider::{AsyncCkChannel, CkProviderImpl, CkStreamProcessorBuilder, StreamConfig};
        use std::time::Duration;

        let ck_config = self.properties.get_ck_config(&self.properties.dws_db_name);
        let db_table_name = format!("{}.dws_test_item_index_cluster", self.properties.dws_db_name);

        info!("开始写入TestItemIndex到ClickHouse，数据量: {}", data.len());

        // 配置流式处理参数 - 小批量处理避免内存问题
        let batch_size = 1000; // 减小批次大小
        let stream_config = StreamConfig::default()
            .with_buffer_size(2000)
            .with_batch_size(batch_size)
            .with_flush_interval(Duration::from_millis(1000))
            .with_max_retries(3)
            .with_backpressure_timeout(Duration::from_secs(300))
            .with_parallel_flush(false) // 禁用并行刷新减少内存使用
            .with_max_concurrent_flushes(1);

        // 创建流式通道
        let (sender, receiver) = AsyncCkChannel::new::<TestItemIndex>(stream_config.clone(), 5);

        let ck_provider = CkProviderImpl::new(ck_config.clone());

        // 创建流处理器
        let mut processor = CkStreamProcessorBuilder::new()
            .with_receiver(receiver)
            .with_provider(ck_provider.clone())
            .with_config(stream_config)
            .with_table_name(db_table_name)
            .build()?;

        // 启动流处理器任务
        let processor_handle = tokio::spawn(async move {
            if let Err(e) = processor.start().await {
                eprintln!("TestItemIndex流处理器错误: {:?}", e);
            }
        });

        // 分批发送数据
        for chunk in data.chunks(batch_size) {
            sender.send_batch(Some(chunk.to_vec())).await?;
        }

        // 发送结束消息
        sender.send(None).await?;

        // 等待处理器完成
        processor_handle.await?;

        info!("成功写入TestItemIndex到ClickHouse");
        Ok(())
    }
}